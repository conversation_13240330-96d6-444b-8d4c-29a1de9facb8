# 训练推理一致性修复说明

## 问题描述

您提到"训练的时候是剪裁上半部分 现在推理也是"，经过代码分析发现了训练和推理时图像处理不一致的问题。

## 问题分析

### 训练时的图像处理
- **位置**: `src/lerobot/datasets/transforms.py` 中的 `CropBottomHalf` 变换
- **处理方式**: **物理裁剪**图像的上半部分，保留下半部分
- **结果**: 图像尺寸从 1280x720 变为 1280x360（高度减半）
- **启用方式**: 训练命令中使用了 `--dataset.image_transforms.enable=true` 和 `crop_bottom_half.weight=1.0`

### 推理时的图像处理（修复前）
- **位置**: `script/act_eval_episode.py` 中的 `mask_image_top_half` 函数
- **处理方式**: 用**黑色遮罩**覆盖图像的上半部分
- **结果**: 图像尺寸保持 1280x720 不变，但上半部分被置为0

### 关键差异
1. **训练时**: 物理裁剪，图像尺寸变化 (720→360)
2. **推理时**: 黑色遮罩，图像尺寸不变 (720→720)

这种不一致会导致模型在推理时看到的图像格式与训练时不同，影响性能。

## 解决方案

### 修改内容
1. **重命名函数**: `mask_image_top_half` → `crop_bottom_half`
2. **修改实现**: 从黑色遮罩改为物理裁剪
3. **更新注释**: 所有相关的注释和帮助文本

### 核心修改
```python
def crop_bottom_half(image_tensor: torch.Tensor) -> torch.Tensor:
    """裁剪图片的下半部分（与训练时的CropBottomHalf变换保持一致）"""
    from torchvision.transforms.functional import crop
    
    if image_tensor.dim() == 3:  # [C, H, W]
        _, height, width = image_tensor.shape
        top = height // 2
        cropped_image = crop(image_tensor, top, 0, height - top, width)
    elif image_tensor.dim() == 4:  # [B, C, H, W]
        _, _, height, width = image_tensor.shape
        top = height // 2
        cropped_image = crop(image_tensor, top, 0, height - top, width)
    
    return cropped_image
```

### 验证结果
- ✅ 3D图像: 720x1280 → 360x1280 (高度减半)
- ✅ 4D图像: 720x1280 → 360x1280 (高度减半)
- ✅ 裁剪方式: 保留下半部分，去掉上半部分

## 使用方法

现在推理脚本的 `--mask_top_half` 参数会执行与训练时完全一致的图像裁剪：

```bash
# 使用裁剪功能（与训练时保持一致）
python script/act_eval_episode.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --episode_idx 1 \
    --plot_interval 3 \
    --save_debug_images 5 \
    --mask_top_half  # 启用图像下半部分裁剪（与训练时保持一致）
```

## 验证结果 ✅

### 功能验证
- ✅ **CropBottomHalf确实保留下半部分**：通过测试确认 `crop(img, height//2, 0, height//2, width)` 保留图像下半部分
- ✅ **推理流程正确**：裁剪操作在模型推理之前执行，模型接收裁剪后的图像
- ✅ **尺寸变化正确**：图像从 1280x720 裁剪为 1280x360（高度减半）

### 代码流程确认
1. **第289行**: `obs_batch[key] = crop_bottom_half(obs_batch[key])` - 应用裁剪
2. **第309行**: `pred_actions = model.predict_action_chunk(obs_batch)` - 使用裁剪后图像推理

## 预期效果

修复后，推理时的图像处理将与训练时完全一致：
- 相同的裁剪方式（物理裁剪而非遮罩）
- 相同的图像尺寸（360高度而非720）
- 相同的视觉内容（只保留下半部分）
- **推理确实使用下半部分图像进行预测**

这应该能够提高模型的推理性能，因为现在推理时看到的图像格式与训练时完全一致。

## 技术细节

### 训练时的变换配置
```python
"crop_bottom_half": ImageTransformConfig(
    weight=1.0,  # 启用裁剪变换
    type="CropBottomHalf",
    kwargs={"debug_save_images": True, "debug_save_dir": "debug_crop_images"},
)
```

### 训练命令中的关键参数
```bash
--dataset.image_transforms.enable=true
--dataset.image_transforms.tfs.crop_bottom_half.weight=1.0
--dataset.image_transforms.tfs.crop_bottom_half.type=CropBottomHalf
```

这确保了训练时图像变换的启用和正确配置。
