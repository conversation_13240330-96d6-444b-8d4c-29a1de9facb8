#!/usr/bin/env python

"""
简单测试脚本，验证保存变换后图像的功能
"""

import torch
from pathlib import Path
from lerobot.datasets.transforms import ImageTransforms, ImageTransformsConfig, SaveTransformedImages

def test_save_transformed_images():
    """测试保存变换后图像的功能"""
    
    # 创建测试配置
    config = ImageTransformsConfig(
        enable=True,
        enable_save_transformed=True,
        save_transformed_dir="test_transformed_images",
        save_transformed_steps=[0, 1, 2],
        save_original_images=False,
        max_num_transforms=2,
    )
    
    # 创建变换实例
    transforms = ImageTransforms(config)
    
    # 创建测试图像 (3, 224, 224) - CHW格式
    test_image = torch.rand(3, 224, 224)
    
    print("开始测试保存变换后图像功能...")
    print(f"测试图像形状: {test_image.shape}")
    print(f"保存目录: {config.save_transformed_dir}")
    print(f"目标步骤: {config.save_transformed_steps}")
    
    # 创建输出目录
    output_dir = Path(config.save_transformed_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 模拟几个训练步骤
    for step in range(5):
        print(f"\n步骤 {step}:")

        # 应用变换（在当前步骤应用变换，此时会检查是否需要保存）
        transformed_image = transforms(test_image)

        print(f"  变换后图像形状: {transformed_image.shape}")

        # 检查是否应该保存图像
        if step in config.save_transformed_steps:
            print(f"  -> 应该在步骤 {step} 保存图像")

        # 增加步骤计数器（模拟训练步骤完成）
        SaveTransformedImages.increment_global_step()
    
    # 清理资源
    SaveTransformedImages.cleanup()
    
    print(f"\n测试完成！请检查 {output_dir} 目录中的保存文件。")
    
    # 列出保存的文件
    if output_dir.exists():
        saved_files = list(output_dir.glob("*.png"))
        if saved_files:
            print(f"保存的文件:")
            for file in saved_files:
                print(f"  - {file.name}")
        else:
            print("没有找到保存的图像文件")

if __name__ == "__main__":
    test_save_transformed_images()
