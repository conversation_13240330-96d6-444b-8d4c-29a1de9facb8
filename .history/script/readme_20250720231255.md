#查看数据集

conda activate lerobot;
python -m lerobot.scripts.visualize_dataset \
    --repo-id=pick_up_parts_less \
    --root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --episode-index=0

# resume training

conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=200000 \
    --save_freq=5000 \
    --wandb.enable=true \
    --wandb.mode=offline

# resume training with data augmentation (从8w步开始启用数据增强)

conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --dataset.image_transforms.random_order=false \
    --wandb.disable_artifact=true



#ACT

conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --output_dir=outputs/train/pick_up_parts_less_act_chunk10_$(date +%Y%m%d_%H%M%S) \
    --job_name=pick_up_parts_less_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=10000 \
    --wandb.disable_artifact=true\
    --batch_size=30 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=10 \
    --policy.chunk_size=10 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0

# ACT resnet50 
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --policy.type=act \
    --policy.vision_backbone=resnet50 \
    --policy.pretrained_backbone_weights=ResNet50_Weights.IMAGENET1K_V1 \
    --policy.optimizer_lr_backbone=1e-6 \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --output_dir=outputs/train/pick_up_parts_less_act_resnet50_$(date +%Y%m%d_%H%M%S) \
    --job_name=pick_up_parts_less_resnet50_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=10000 \
    --wandb.disable_artifact=true\
    --log_freq=500\
    --batch_size=16 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=10 \
    --policy.chunk_size=10 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0



# ACT模型离线评估

# 基本评估（使用完整数据集）
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_20250717_172222/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --n_samples 100 \
    --output_file scripts/act_eval_random_results.json

# 使用指定episodes评估
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --episodes "[10,11,12,13,14]" \
    --n_samples 10 \
    --output_file scripts/act_eval_specified_episodes_results.json

# 单个episode可视化（默认每3步画一条线）
conda activate lerobot && python script/act_eval_episode.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/pick_up_parts_less/lerobot \
    --episode_idx 1 \
    --plot_interval 3 \
    --save_debug_images 5

# Diffusion Policy 训练 - 针对快速动态抓取优化配置

# 原始配置（存在问题，仅供参考）
# conda activate lerobot;
# python -m lerobot.scripts.train \
#     --dataset.repo_id=pick_up_parts_less \
#     --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
#     --dataset.image_transforms.enable=true \
#     --dataset.image_transforms.max_num_transforms=3 \
#     --policy.type=diffusion \
#     --policy.push_to_hub=false \
#     --output_dir=outputs/train/pick_up_parts_less_diffusion_$(date +%Y%m%d_%H%M%S) \
#     --job_name=pick_up_parts_less_diffusion_training \
#     --policy.device=cuda \
#     --steps=20000 \
#     --log_freq=5 \
#     --save_freq=200 \
#     --batch_size=200 \
#     --num_workers=2 \
#     --wandb.enable=true \
#     --wandb.mode=offline \
#     --wandb.project=pick_up_parts_diffusion_training \
#     --policy.n_obs_steps=2 \
#     --policy.horizon=16 \
#     --policy.n_action_steps=15 \
#     --policy.num_train_timesteps=100 \
#     --policy.num_inference_steps=20 \
#     --policy.down_dims='[512,1024,2048]' \
#     --policy.diffusion_step_embed_dim=256 \
#     --policy.use_film_scale_modulation=true \
#     --policy.optimizer_lr=2e-4 \
#     --policy.optimizer_weight_decay=1e-6 \
#     --scheduler.type=cosine_decay_with_warmup \
#     --scheduler.num_warmup_steps=1000 \
#     --scheduler.num_decay_steps=100000 \
#     --scheduler.peak_lr=2e-4 \
#     --scheduler.decay_lr=2e-6 \
#     --eval_freq=0

# 优化配置1：针对快速动态抓取的参数调整
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --policy.type=diffusion \
    --policy.push_to_hub=false \
    --output_dir=outputs/train/pick_up_parts_less_diffusion_optimized_$(date +%Y%m%d_%H%M%S) \
    --job_name=pick_up_parts_less_diffusion_optimized \
    --policy.device=cuda \
    --steps=20000 \
    --log_freq=10 \
    --save_freq=500 \
    --batch_size=200 \
    --num_workers=6 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_diffusion_optimized \
    --policy.n_obs_steps=2 \
    --policy.horizon=16 \
    --policy.num_train_timesteps=1000 \
    --policy.num_inference_steps=50 \
    --policy.down_dims='[256,512,1024]' \
    --policy.diffusion_step_embed_dim=128 \
    --policy.use_film_scale_modulation=true \
    --policy.optimizer_lr=1e-4 \
    --policy.optimizer_weight_decay=1e-6 \
    --scheduler.type=cosine_decay_with_warmup \
    --scheduler.num_warmup_steps=2000 \
    --scheduler.num_decay_steps=18000 \
    --scheduler.peak_lr=1e-4 \
    --scheduler.decay_lr=1e-6 \
    --eval_freq=0

# 优化配置2：GPU利用率优化（如果内存足够）
# conda activate lerobot;
# python -m lerobot.scripts.train \
#     --dataset.repo_id=pick_up_parts_less \
#     --dataset.root=/home/<USER>/data/pick_up_parts_less/lerobot \
#     --dataset.image_transforms.enable=true \
#     --dataset.image_transforms.max_num_transforms=2 \
#     --policy.type=diffusion \
#     --policy.push_to_hub=false \
#     --output_dir=outputs/train/pick_up_parts_less_diffusion_gpu_opt_$(date +%Y%m%d_%H%M%S) \
#     --job_name=pick_up_parts_less_diffusion_gpu_opt \
#     --policy.device=cuda \
#     --steps=20000 \
#     --log_freq=10 \
#     --save_freq=500 \
#     --batch_size=96 \
#     --num_workers=8 \
#     --wandb.enable=true \
#     --wandb.mode=offline \
#     --wandb.project=pick_up_parts_diffusion_gpu_opt \
#     --policy.n_obs_steps=4 \
#     --policy.horizon=24 \
#     --policy.n_action_steps=6 \
#     --policy.num_train_timesteps=1000 \
#     --policy.num_inference_steps=50 \
#     --policy.down_dims='[256,512,1024]' \
#     --policy.diffusion_step_embed_dim=128 \
#     --policy.use_film_scale_modulation=true \
#     --policy.use_amp=true \
#     --policy.optimizer_lr=1e-4 \
#     --policy.optimizer_weight_decay=1e-6 \
#     --scheduler.type=cosine_decay_with_warmup \
#     --scheduler.num_warmup_steps=2000 \
#     --scheduler.num_decay_steps=18000 \
#     --scheduler.peak_lr=1e-4 \
#     --scheduler.decay_lr=1e-6 \
#     --eval_freq=0

# PI0 训练配置

# PI0 微调

conda activate lerobot && python -m lerobot.scripts.train \
    --policy.path=lerobot/pi0 \
    --dataset.repo_id=stock_cloth_2 \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --policy.push_to_hub=false \
    --policy.device=cuda \
    --output_dir=outputs/train/pi0_stock_cloth_chunk000_$(date +%Y%m%d_%H%M%S) \
    --job_name=pi0_stock_cloth_chunk000_training \
    --steps=50000 \
    --save_freq=500 \
    --log_freq=50 \
    --wandb.project=lerobot \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --batch_size=2 \
    --eval_freq=0 \
    --policy.train_expert_only=true \
    --policy.freeze_vision_encoder=true \
    --policy.train_state_proj=true \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --dataset.video_backend=pyav


# PI0 resume training - 恢复训练
conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/pi0_stock_cloth_chunk000_20250720_225727/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=1000000 \
    --save_freq=5000 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --eval_freq=0 \
    --log_freq=500 



