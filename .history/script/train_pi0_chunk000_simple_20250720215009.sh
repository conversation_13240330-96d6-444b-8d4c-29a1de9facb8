#!/bin/bash

# 激活conda环境
source /home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate lerobot

# 创建一个临时的episodes列表文件
echo "[$(seq -s, 0 999)]" > episodes.txt

python -m lerobot.scripts.train \
--policy.type=pi0 \
--dataset.repo_id=stock_cloth_2 \
--dataset.root=/home/<USER>/data/stock_cloth_2 \
--dataset.episodes="$(cat episodes.txt)" \
--output_dir=outputs/train/pi0_stock_cloth_chunk000 \
--job_name=pi0_stock_cloth_chunk000 \
--steps=50000 \
--save_freq=5000 \
--log_freq=500 \
--wandb.project=lerobot \
--wandb.enable=true \
--wandb.mode=offline \
--wandb.disable_artifact=true \
--batch_size=16 \
--eval_freq=0 \
--policy.train_expert_only=true \
--policy.freeze_vision_encoder=true \
--policy.train_state_proj=true \
--dataset.image_transforms.enable=true \
--dataset.image_transforms.max_num_transforms=2 \
--dataset.video_backend=pyav

rm episodes.txt
