#!/usr/bin/env python3
"""
PI0模型单个episode可视化脚本

可视化单个episode中的ground truth actions和模型预测的action chunks。
- Ground truth actions用黑色线画出
- 模型预测的action chunks用不同颜色的线段画出
- 预测的第一个值用圆点表示
- 支持图像遮罩功能：用黑色遮住图片上半部分以减少干扰

使用方法:
# 不使用遮罩
python script/pi0eval_episode.py \
    --model_path outputs/train/pi0_stock_cloth_effort_chunk000_20250720_232952/checkpoints/last/pretrained_model \
    --dataset.repo_id=stock_cloth_2 \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --episode_idx 1 \
    --plot_interval 3 \
    --save_debug_images 5

# 使用遮罩功能
python script/pi0eval_episode.py \
    --model_path lerobot/pi0 \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --episode_idx 1 \
    --plot_interval 3 \
    --save_debug_images 5 \
    --mask_top_half  # 启用图像上半部分遮罩

"""

import argparse
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List
import matplotlib.colors as mcolors
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo

from lerobot.datasets.lerobot_dataset import LeRobotDataset
from lerobot.policies.pi0.modeling_pi0 import PI0Policy
from lerobot.utils.utils import get_safe_torch_device


def parse_args():
    parser = argparse.ArgumentParser(description="PI0模型单个episode可视化")
    parser.add_argument("--model_path", type=str, required=True,
                       help="训练好的PI0模型路径")
    parser.add_argument("--dataset_repo_id", type=str, required=True,
                       help="数据集repo ID")
    parser.add_argument("--dataset_root", type=str, required=True,
                       help="数据集根目录")
    parser.add_argument("--episode_idx", type=int, default=0,
                       help="要可视化的episode索引 (默认: 0)")
    parser.add_argument("--device", type=str, default="cuda",
                       help="设备 (默认: cuda)")
    parser.add_argument("--output_dir", type=str, default="pi0_eval_plots",
                       help="输出图片目录 (默认: pi0_eval_plots)")
    parser.add_argument("--max_steps", type=int, default=None,
                       help="最大步数限制 (默认: None，使用整个episode)")
    parser.add_argument("--plot_interval", type=int, default=3,
                       help="绘图间隔，每隔几步画一条预测线 (默认: 3)")
    parser.add_argument("--save_debug_images", type=int, default=5,
                       help="保存前N步的输入图像用于debug (默认: 5, 设为0则不保存)")
    parser.add_argument("--mask_top_half", action="store_true",
                       help="用黑色遮住图片的上半部分以减少干扰 (默认: False)")
    parser.add_argument("--language_instruction", type=str, default="",
                       help="语言指令 (默认: 空字符串)")
    return parser.parse_args()


def get_episode_data(dataset: LeRobotDataset, episode_idx: int) -> Dict:
    """获取指定episode的所有数据 - 使用LeRobot的episode_data_index"""
    print(f"正在获取Episode {episode_idx}的数据...")

    try:
        # 使用LeRobot的episode_data_index直接获取episode范围
        if not hasattr(dataset, 'episode_data_index') or dataset.episode_data_index is None:
            print("episode_data_index不可用，使用备用方法...")
            return get_episode_data_fallback(dataset, episode_idx)

        # 检查episode是否存在
        if episode_idx >= len(dataset.episode_data_index["from"]):
            raise ValueError(f"Episode {episode_idx} not found. Dataset has {len(dataset.episode_data_index['from'])} episodes")

        # 获取episode的起始和结束索引
        from_idx = dataset.episode_data_index["from"][episode_idx].item()
        to_idx = dataset.episode_data_index["to"][episode_idx].item()

        print(f"Episode {episode_idx}: 索引范围 [{from_idx}, {to_idx}), 共 {to_idx - from_idx} 步")

        # 直接通过索引获取所有数据
        episode_data = []
        episode_indices = list(range(from_idx, to_idx))

        for idx in episode_indices:
            sample = dataset[idx]
            episode_data.append(sample)

        return {
            "indices": episode_indices,
            "data": episode_data,
            "length": len(episode_data)
        }

    except Exception as e:
        print(f"使用episode_data_index失败: {e}")
        print("使用备用方法...")
        return get_episode_data_fallback(dataset, episode_idx)


def get_episode_data_fallback(dataset: LeRobotDataset, episode_idx: int) -> Dict:
    """备用方法：直接创建单episode数据集"""
    print(f"使用备用方法获取Episode {episode_idx}...")

    try:
        # 尝试直接创建只包含指定episode的数据集
        single_episode_dataset = LeRobotDataset(
            dataset.repo_id,
            root=dataset.root,
            episodes=[episode_idx]
        )

        print(f"Episode {episode_idx} has {len(single_episode_dataset)} steps")

        # 获取所有数据
        episode_data = []
        episode_indices = list(range(len(single_episode_dataset)))

        for idx in episode_indices:
            sample = single_episode_dataset[idx]
            episode_data.append(sample)

        return {
            "indices": episode_indices,
            "data": episode_data,
            "length": len(episode_data)
        }

    except Exception as e:
        print(f"备用方法也失败: {e}")
        raise ValueError(f"无法获取Episode {episode_idx}的数据")


def save_input_images(obs_batch: Dict, step_idx: int, output_dir: str, original_obs_batch: Dict = None):
    """保存输入图像用于debug

    Args:
        obs_batch: 处理后的观测数据 (已应用遮罩)
        step_idx: 步数索引
        output_dir: 输出目录
        original_obs_batch: 原始观测数据 (未应用遮罩)，如果提供则会保存对比图像
    """
    import os
    from PIL import Image

    # 创建图像保存目录
    img_save_dir = os.path.join(output_dir, "debug_images")
    os.makedirs(img_save_dir, exist_ok=True)

    # 转换tensor到PIL图像的函数
    def tensor_to_pil(tensor):
        # tensor shape: [1, C, H, W] -> [C, H, W]
        if tensor.dim() == 4:
            tensor = tensor.squeeze(0)
        # 如果是归一化的[0,1]范围，转换到[0,255]
        if tensor.max() <= 1.0:
            tensor = tensor * 255
        tensor = tensor.clamp(0, 255).byte()
        # [C, H, W] -> [H, W, C]
        if tensor.shape[0] == 3:  # RGB
            tensor = tensor.permute(1, 2, 0)
        elif tensor.shape[0] == 1:  # Grayscale
            tensor = tensor.squeeze(0)
        return Image.fromarray(tensor.cpu().numpy())

    # 保存所有图像观测
    for key, value in obs_batch.items():
        if 'image' in key.lower() and hasattr(value, 'shape'):
            try:
                if len(value.shape) >= 3:  # 确保是图像tensor
                    # 根据是否有原始图像决定文件名
                    if original_obs_batch and key in original_obs_batch:
                        # 保存遮罩后的图像
                        pil_img = tensor_to_pil(value)
                        img_filename = f"step_{step_idx:03d}_{key.replace('.', '_').replace('/', '_')}_masked.png"
                        img_path = os.path.join(img_save_dir, img_filename)
                        pil_img.save(img_path)
                        print(f"💾 保存遮罩图像: {img_path} (shape: {value.shape})")

                        # 保存原始图像用于对比
                        original_pil_img = tensor_to_pil(original_obs_batch[key])
                        original_img_filename = f"step_{step_idx:03d}_{key.replace('.', '_').replace('/', '_')}_original.png"
                        original_img_path = os.path.join(img_save_dir, original_img_filename)
                        original_pil_img.save(original_img_path)
                        print(f"💾 保存原始图像: {original_img_path}")
                    else:
                        # 保存普通图像
                        pil_img = tensor_to_pil(value)
                        img_filename = f"step_{step_idx:03d}_{key.replace('.', '_').replace('/', '_')}.png"
                        img_path = os.path.join(img_save_dir, img_filename)
                        pil_img.save(img_path)
                        print(f"💾 保存图像: {img_path} (shape: {value.shape})")

            except Exception as e:
                print(f"❌ 保存图像失败 {key}: {e}")


def mask_image_top_half(image_tensor: torch.Tensor) -> torch.Tensor:
    """用黑色遮住图片的上半部分

    Args:
        image_tensor: 形状为 [B, C, H, W] 或 [C, H, W] 的图像tensor

    Returns:
        遮住上半部分的图像tensor
    """
    if image_tensor.dim() == 3:
        # [C, H, W] 格式
        _, height, _ = image_tensor.shape
        masked_image = image_tensor.clone()
        masked_image[:, :height//2, :] = 0  # 上半部分设为黑色 (0)
    elif image_tensor.dim() == 4:
        # [B, C, H, W] 格式
        _, _, height, _ = image_tensor.shape
        masked_image = image_tensor.clone()
        masked_image[:, :, :height//2, :] = 0  # 上半部分设为黑色 (0)
    else:
        raise ValueError(f"不支持的图像tensor维度: {image_tensor.dim()}")

    return masked_image


def predict_action_chunks(model: PI0Policy, episode_data: List[Dict], device: torch.device,
                         output_dir: str = None, save_first_n_images: int = 5,
                         mask_top_half: bool = False, language_instruction: str = "") -> List[torch.Tensor]:
    """对episode中的每个时间步预测action chunk，并可选择保存前N步的输入图像"""
    model.eval()
    predicted_chunks = []
    chunk_info_printed = False

    print(f"🤖 开始使用PI0模型预测 {len(episode_data)} 步的action chunks...")
    print(f"📱 模型设备: {next(model.parameters()).device}")
    print(f"🎯 输入设备: {device}")
    print(f"🗣️  语言指令: '{language_instruction}'" if language_instruction else "🗣️  语言指令: 无")
    if mask_top_half:
        print("🎭 将对所有图像应用上半部分黑色遮罩以减少干扰")
    else:
        print("📷 使用原始图像，不应用遮罩")
    if save_first_n_images > 0 and output_dir:
        print(f"🖼️  将保存前 {save_first_n_images} 步的输入图像到 {output_dir}/debug_images/")

    with torch.no_grad():
        for i, sample in enumerate(episode_data):
            try:
                # 准备观测数据
                obs_keys = [k for k in sample.keys() if k.startswith('observation')]
                if not obs_keys:
                    print(f"⚠️  Warning: No observation keys found in step {i}")
                    predicted_chunks.append(None)
                    continue

                obs_batch = {}
                for key in obs_keys:
                    if hasattr(sample[key], 'to'):
                        obs_batch[key] = sample[key].unsqueeze(0).to(device)
                    else:
                        obs_batch[key] = sample[key]

                # 添加语言指令到观测数据中
                if language_instruction:
                    obs_batch['observation.language_instruction'] = language_instruction

                # 💾 保存原始图像用于对比 (在应用遮罩之前，只有启用遮罩时才需要)
                original_obs_batch = None
                if i < save_first_n_images and output_dir and mask_top_half:
                    # 深拷贝原始观测数据
                    original_obs_batch = {}
                    for key, val in obs_batch.items():
                        if hasattr(val, 'clone'):
                            original_obs_batch[key] = val.clone()
                        else:
                            original_obs_batch[key] = val

                # 🎭 根据参数决定是否对所有图像观测应用上半部分黑色遮罩
                if mask_top_half:
                    for key in obs_batch.keys():
                        if 'image' in key.lower() and hasattr(obs_batch[key], 'shape'):
                            if len(obs_batch[key].shape) >= 3:  # 确保是图像tensor
                                obs_batch[key] = mask_image_top_half(obs_batch[key])
                                if i == 0:
                                    print(f"🎭 对 {key} 应用了上半部分黑色遮罩")

                # 🔥 关键：确认这里真的在调用模型预测
                if i == 0:
                    print(f"🔍 第一步观测数据keys: {list(obs_batch.keys())}")
                    for key, val in obs_batch.items():
                        if hasattr(val, 'shape'):
                            print(f"   {key}: {val.shape}")

                # 💾 保存前N步的输入图像用于debug
                if i < save_first_n_images and output_dir:
                    if mask_top_half:
                        print(f"🖼️  保存第 {i+1} 步的输入图像 (原始 + 遮罩对比)...")
                    else:
                        print(f"🖼️  保存第 {i+1} 步的输入图像...")
                    save_input_images(obs_batch, i, output_dir, original_obs_batch)

                # PI0模型预测逻辑：
                # 1. 重置action queue以获取新的chunk
                # 2. 调用select_action会触发model.sample_actions生成完整chunk
                # 3. 我们需要访问内部生成的完整chunk而不是逐个获取action

                model.reset()  # 重置action queue

                # 直接调用底层的sample_actions方法来获取完整chunk
                images, img_masks = model.prepare_images(obs_batch)
                state = model.prepare_state(obs_batch)
                lang_tokens, lang_masks = model.prepare_language(obs_batch)

                # 调用模型的sample_actions方法获取完整的action chunk
                actions = model.model.sample_actions(
                    images, img_masks, lang_tokens, lang_masks, state, noise=None
                )

                # Unpad actions (与PI0Policy.select_action中的处理一致)
                original_action_dim = model.config.action_feature.shape[0]
                actions = actions[:, :, :original_action_dim]

                # 反归一化
                actions = model.unnormalize_outputs({"action": actions})["action"]

                if model.config.adapt_to_pi_aloha:
                    actions = model._pi_aloha_encode_actions(actions)

                # actions shape: [batch_size, n_action_steps, action_dim]
                # 我们取第一个batch的所有action steps
                pred_chunk = actions[0].cpu()  # [n_action_steps, action_dim]
                predicted_chunks.append(pred_chunk)

                # 打印第一个chunk的详细信息
                if not chunk_info_printed:
                    print(f"✅ 模型预测成功!")
                    print(f"📊 Chunk shape: {pred_chunk.shape}")
                    print(f"📈 Chunk数值范围: [{pred_chunk.min().item():.4f}, {pred_chunk.max().item():.4f}]")
                    print(f"📏 Chunk size: {pred_chunk.shape[0]}, Action dim: {pred_chunk.shape[1]}")
                    chunk_info_printed = True

                if (i + 1) % 20 == 0:
                    print(f"🔄 已预测 {i + 1}/{len(episode_data)} 步")

            except Exception as e:
                print(f"❌ Error predicting step {i}: {e}")
                predicted_chunks.append(None)
                continue

    successful_predictions = sum(1 for chunk in predicted_chunks if chunk is not None)
    print(f"🎉 预测完成: {successful_predictions}/{len(episode_data)} chunks 成功")

    return predicted_chunks


def generate_distinct_colors(n_colors: int) -> List[str]:
    """生成n个明显不同的颜色"""
    if n_colors <= 10:
        # 使用预定义的明显不同的颜色
        colors = ['red', 'blue', 'green', 'orange', 'purple',
                 'brown', 'pink', 'gray', 'olive', 'cyan']
        return colors[:n_colors]
    else:
        # 使用HSV色彩空间生成均匀分布的颜色
        colors = []
        for i in range(n_colors):
            hue = i / n_colors
            color = mcolors.hsv_to_rgb([hue, 0.8, 0.9])
            colors.append(color)
        return colors


def plot_episode_actions_html(episode_data: List[Dict], predicted_chunks: List[torch.Tensor],
                             output_dir: str, episode_idx: int, max_steps: int = None, plot_interval: int = 3):
    """使用Plotly生成交互式HTML可视化

    Args:
        episode_data: episode数据
        predicted_chunks: 预测的action chunks
        output_dir: 输出目录
        episode_idx: episode索引
        max_steps: 最大步数限制
        plot_interval: 绘图间隔，每隔几步画一条预测线（默认3步）
    """

    # 提取ground truth actions
    gt_actions = []
    for sample in episode_data:
        if 'action' in sample:
            action = sample['action']
            if len(action.shape) > 1:
                action = action[-1, :]  # 取最后一个时间步
            gt_actions.append(action.cpu().numpy())

    if not gt_actions:
        print("No ground truth actions found")
        return

    gt_actions = np.array(gt_actions)
    action_dim = gt_actions.shape[1]

    # 限制步数
    if max_steps is not None:
        gt_actions = gt_actions[:max_steps]
        predicted_chunks = predicted_chunks[:max_steps]
        episode_data = episode_data[:max_steps]

    n_steps = len(gt_actions)

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    print(f"🎨 开始生成交互式HTML图表 ({action_dim} 维度, {n_steps} 步)...")

    # 创建子图 - 减小间距让每个灰色绘图区域更大
    vertical_spacing = max(0.01, min(0.05, 0.5 / action_dim))  # 更小的间距
    fig = make_subplots(
        rows=action_dim, cols=1,
        subplot_titles=[f'Action Dimension {i}' for i in range(action_dim)],
        vertical_spacing=vertical_spacing,
        shared_xaxes=True
    )

    # 颜色列表
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'olive', 'cyan', 'magenta']

    for dim in range(action_dim):
        row = dim + 1

        # 添加Ground Truth线
        time_steps = list(range(n_steps))
        fig.add_trace(
            go.Scatter(
                x=time_steps,
                y=gt_actions[:, dim],
                mode='lines',
                name=f'Ground Truth (Dim {dim})',
                line=dict(color='black', width=3),
                showlegend=(dim == 0)  # 只在第一个子图显示图例
            ),
            row=row, col=1
        )

        # 添加预测chunks - 按照plot_interval间隔绘制
        chunk_count = 0
        for step_idx, chunk in enumerate(predicted_chunks):
            if chunk is None:
                continue

            # 只绘制间隔步数的预测线，但仍然计算所有步数的误差
            if step_idx % plot_interval != 0:
                continue

            try:
                # 处理chunk维度 - PI0的chunk格式为[chunk_size, action_dim]
                if len(chunk.shape) == 2:  # [chunk_size, action_dim]
                    chunk_actions = chunk[:, dim].numpy()
                elif len(chunk.shape) == 3:  # [batch, chunk_size, action_dim]
                    chunk_actions = chunk[0, :, dim].numpy()
                else:  # [action_dim] 单个action
                    chunk_actions = np.array([chunk[dim].numpy()])

                chunk_size = len(chunk_actions)
                chunk_time_steps = list(range(step_idx, step_idx + chunk_size))

                # 确保不超出边界
                valid_indices = [i for i, t in enumerate(chunk_time_steps) if t < n_steps]
                chunk_time_steps = [chunk_time_steps[i] for i in valid_indices]
                chunk_actions = chunk_actions[valid_indices]

                if len(chunk_actions) > 0:
                    # 使用step_idx除以plot_interval来确定颜色，这样相邻的绘制线条颜色不同
                    color_idx = (step_idx // plot_interval) % len(colors)
                    color = colors[color_idx]

                    # 添加预测线
                    fig.add_trace(
                        go.Scatter(
                            x=chunk_time_steps,
                            y=chunk_actions,
                            mode='lines',
                            name=f'Pred {step_idx} (Dim {dim})' if dim == 0 and step_idx < 8 else None,
                            line=dict(color=color, width=2),
                            opacity=0.7,
                            showlegend=(dim == 0 and step_idx < 8)
                        ),
                        row=row, col=1
                    )

                    # 添加第一个预测点（使用小三角形，减少遮挡）
                    fig.add_trace(
                        go.Scatter(
                            x=[chunk_time_steps[0]],
                            y=[chunk_actions[0]],
                            mode='markers',
                            name=None,
                            marker=dict(
                                color=color,
                                size=4,  # 减小尺寸
                                symbol='triangle-up',  # 改为三角形
                                line=dict(color='white', width=0.5)  # 减小边框
                            ),
                            showlegend=False
                        ),
                        row=row, col=1
                    )

                    chunk_count += 1

            except Exception as e:
                print(f"❌ Error processing chunk at step {step_idx}: {e}")
                continue

    # 更新布局 - 大幅增加每个子图的显示高度
    fig.update_layout(
        title=f'Episode {episode_idx} - PI0 Action Predictions vs Ground Truth ({chunk_count} chunks)',
        height=800 * action_dim,  # 每个子图800像素高度，让灰色绘图区域更大
        showlegend=True,
        legend=dict(x=1.02, y=1),
        hovermode='x unified',
        # 增加子图内部的绘图区域大小
        margin=dict(l=80, r=80, t=100, b=80)
    )

    # 更新x轴
    fig.update_xaxes(title_text="Time Step", row=action_dim, col=1)

    # 更新y轴 - 使用合适的数值范围，重点是增大绘图区域
    for dim in range(action_dim):
        # 计算该维度的数据范围，适当扩展以显示细节
        dim_values = gt_actions[:, dim]
        y_min, y_max = dim_values.min(), dim_values.max()
        y_padding = max(0.01, (y_max - y_min) * 0.1)  # 10%的边距

        fig.update_yaxes(
            title_text=f"Action Dim {dim}",
            range=[y_min - y_padding, y_max + y_padding],
            row=dim+1, col=1
        )

    print(f"✅ HTML图表生成成功，包含 {chunk_count} 个预测chunks")
    return fig


def compute_prediction_stats(episode_data: List[Dict], predicted_chunks: List[torch.Tensor]) -> Dict:
    """计算预测统计信息"""
    stats = {
        "total_steps": len(episode_data),
        "successful_predictions": 0,
        "chunk_sizes": [],
        "chunk_errors": []
    }

    for i, (sample, chunk) in enumerate(zip(episode_data, predicted_chunks)):
        if chunk is None or 'action' not in sample:
            continue

        stats["successful_predictions"] += 1

        try:
            # 获取ground truth action
            gt_action = sample['action']
            if len(gt_action.shape) > 1:
                gt_action = gt_action[-1, :]  # 取最后一个时间步
            gt_action = gt_action.cpu().numpy()

            # 处理预测的chunk - PI0格式为[chunk_size, action_dim]
            if len(chunk.shape) == 2:  # [chunk_size, action_dim]
                chunk_actions = chunk.numpy()
                stats["chunk_sizes"].append(chunk.shape[0])
            elif len(chunk.shape) == 3:  # [batch, chunk_size, action_dim]
                chunk_actions = chunk[0].numpy()  # [chunk_size, action_dim]
                stats["chunk_sizes"].append(chunk.shape[1])
            else:
                continue

            # 计算整个chunk的平均误差（如果有对应的GT）
            chunk_size = len(chunk_actions)
            remaining_steps = len(episode_data) - i
            actual_chunk_size = min(chunk_size, remaining_steps)

            chunk_gt = []
            for j in range(actual_chunk_size):
                if i + j < len(episode_data) and 'action' in episode_data[i + j]:
                    gt_j = episode_data[i + j]['action']
                    if len(gt_j.shape) > 1:
                        gt_j = gt_j[-1, :]
                    chunk_gt.append(gt_j.cpu().numpy())

            if len(chunk_gt) > 0:
                chunk_gt = np.array(chunk_gt)
                chunk_pred = chunk_actions[:len(chunk_gt)]
                chunk_error = np.mean((chunk_pred - chunk_gt) ** 2)
                stats["chunk_errors"].append(chunk_error)

        except Exception as e:
            print(f"Error computing stats for step {i}: {e}")
            continue

    # 计算平均值
    if stats["chunk_errors"]:
        stats["avg_chunk_mse"] = np.mean(stats["chunk_errors"])
        stats["avg_chunk_rmse"] = np.sqrt(stats["avg_chunk_mse"])

    if stats["chunk_sizes"]:
        stats["avg_chunk_size"] = np.mean(stats["chunk_sizes"])
        stats["unique_chunk_sizes"] = list(set(stats["chunk_sizes"]))

    return stats


def print_stats(stats: Dict):
    """打印统计信息"""
    print("\n" + "=" * 50)
    print("预测统计信息 (PI0 Chunk评估模式)")
    print("=" * 50)
    print(f"总步数: {stats['total_steps']}")
    print(f"成功预测: {stats['successful_predictions']}")
    print(f"成功率: {stats['successful_predictions']/stats['total_steps']*100:.1f}%")
    print(f"评估方式: 整体chunk评估（每个预测与其对应时刻的真值比较）")

    if "avg_chunk_size" in stats:
        print(f"平均chunk大小: {stats['avg_chunk_size']:.1f}")
        print(f"chunk大小范围: {stats['unique_chunk_sizes']}")

    if "avg_chunk_rmse" in stats:
        print(f"Chunk RMSE: {stats['avg_chunk_rmse']:.6f}")
        print(f"Chunk角度误差: {stats['avg_chunk_rmse'] * 180 / np.pi:.2f}°")

    print("=" * 50)


def main():
    args = parse_args()

    print("=" * 70)
    print("PI0模型单个Episode可视化")
    print("=" * 70)
    print(f"模型路径: {args.model_path}")
    print(f"数据集: {args.dataset_repo_id}")
    print(f"Episode索引: {args.episode_idx}")
    print(f"设备: {args.device}")
    print(f"绘图间隔: 每{args.plot_interval}步画一条线")
    print(f"Debug图像: 保存前{args.save_debug_images}步的输入图像" if args.save_debug_images > 0 else "Debug图像: 不保存")
    print(f"图像遮罩: {'启用上半部分黑色遮罩' if args.mask_top_half else '使用原始图像'}")
    print(f"语言指令: '{args.language_instruction}'" if args.language_instruction else "语言指令: 无")
    print("=" * 70)

    # 检查设备
    device = get_safe_torch_device(args.device, log=True)

    try:
        # 加载模型
        print("🔄 正在加载PI0模型...")
        model = PI0Policy.from_pretrained(args.model_path)
        model.eval()
        model.to(device)
        print(f"✅ 模型加载成功: {type(model).__name__}")
        print(f"📋 模型配置:")
        print(f"   - Chunk size: {model.config.chunk_size}")
        print(f"   - Action steps: {model.config.n_action_steps}")
        print(f"   - 模型参数量: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   - 模型设备: {next(model.parameters()).device}")

        # 加载数据集
        print("正在加载数据集...")
        dataset = LeRobotDataset(args.dataset_repo_id, root=args.dataset_root)
        print(f"数据集大小: {len(dataset)}")

        # 获取episode数据
        print(f"正在获取Episode {args.episode_idx}的数据...")
        episode_info = get_episode_data(dataset, args.episode_idx)
        episode_data = episode_info["data"]

        # 限制步数
        if args.max_steps is not None:
            episode_data = episode_data[:args.max_steps]
            print(f"限制到前 {len(episode_data)} 步")

        # 预测action chunks (可选保存前N步的输入图像)
        predicted_chunks = predict_action_chunks(model, episode_data, device,
                                                args.output_dir, save_first_n_images=args.save_debug_images,
                                                mask_top_half=args.mask_top_half,
                                                language_instruction=args.language_instruction)

        # 计算统计信息
        stats = compute_prediction_stats(episode_data, predicted_chunks)
        print_stats(stats)

        # 可视化
        print(f"正在生成交互式HTML可视化 (绘图间隔: 每{args.plot_interval}步)...")
        try:
            fig = plot_episode_actions_html(episode_data, predicted_chunks, args.output_dir,
                                           args.episode_idx, args.max_steps, args.plot_interval)

            print(f"🔍 生成的figure类型: {type(fig)}")
            if fig is None:
                raise ValueError("HTML可视化函数返回了None")

            # 保存HTML文件
            output_path = Path(args.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            html_file = output_path / f"episode_{args.episode_idx}_pi0_actions.html"

            # 保存为HTML - 修复plotly版本兼容性问题
            fig.write_html(str(html_file))

            # 尝试自动打开浏览器
            try:
                import webbrowser
                webbrowser.open(f'file://{html_file.absolute()}')
            except Exception:
                pass

            print(f"✅ 交互式HTML图表已保存: {html_file}")
            print(f"🌐 可以在浏览器中打开查看，支持缩放和交互")

        except ImportError:
            print("❌ Plotly未安装，请运行: pip install plotly")
            print("🔄 回退到matplotlib可视化...")
            # 这里可以添加matplotlib的备用方案
        except Exception as e:
            print(f"❌ HTML可视化失败: {e}")

        print("可视化完成!")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
