#!/usr/bin/env python3
"""
启用/禁用图像裁剪变换和步骤图像保存的脚本

这个脚本可以动态修改transforms.py中变换的权重，
从而启用或禁用图像裁剪功能和步骤图像保存功能。

用法:
    python script/enable_crop_transform.py --enable        # 启用裁剪
    python script/enable_crop_transform.py --disable       # 禁用裁剪
    python script/enable_crop_transform.py --enable-debug  # 启用裁剪+debug
    python script/enable_crop_transform.py --save-steps    # 保存步骤图像(0,10,20,30,40,50)
"""

import argparse
import re
from pathlib import Path

def modify_transform_config(enable_crop: bool = True, debug: bool = False, save_steps: bool = False):
    """修改transforms.py中变换的配置"""
    
    transforms_file = Path(__file__).parent.parent / "src" / "lerobot" / "datasets" / "transforms.py"
    
    if not transforms_file.exists():
        print(f"❌ 找不到文件: {transforms_file}")
        return False
    
    # 读取文件内容
    with open(transforms_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 准备替换的配置
    success = True

    # 处理裁剪变换配置
    if enable_crop:
        if debug:
            crop_config = '''            "crop_bottom_half": ImageTransformConfig(
                weight=1.0,  # 启用裁剪变换
                type="CropBottomHalf",
                kwargs={"debug_save_images": True, "debug_save_dir": "debug_crop_images"},
            ),'''
            print("🔍 启用图像裁剪 + Debug模式")
        else:
            crop_config = '''            "crop_bottom_half": ImageTransformConfig(
                weight=1.0,  # 启用裁剪变换
                type="CropBottomHalf",
                kwargs={},
            ),'''
            print("✂️  启用图像裁剪")
    else:
        crop_config = '''            "crop_bottom_half": ImageTransformConfig(
                weight=0.0,  # 禁用裁剪变换
                type="CropBottomHalf",
                kwargs={},
            ),'''
        print("❌ 禁用图像裁剪")

    # 处理步骤图像保存配置
    if save_steps:
        step_config = '''            "save_step_images": ImageTransformConfig(
                weight=1.0,  # 启用步骤图像保存
                type="SaveStepImages",
                kwargs={"save_dir": "step_images", "target_steps": [0, 10, 20, 30, 40, 50]},
            ),'''
        print("📸 启用步骤图像保存 (步骤: 0, 10, 20, 30, 40, 50)")
    else:
        step_config = '''            "save_step_images": ImageTransformConfig(
                weight=0.0,  # 禁用步骤图像保存
                type="SaveStepImages",
                kwargs={"save_dir": "step_images", "target_steps": [0, 10, 20, 30, 40, 50]},
            ),'''
    
    # 使用正则表达式替换crop_bottom_half配置
    crop_pattern = r'"crop_bottom_half":\s*ImageTransformConfig\([^}]+\}[^,]*,?\s*\),'

    if re.search(crop_pattern, content, re.DOTALL):
        content = re.sub(crop_pattern, crop_config, content, flags=re.DOTALL)
    else:
        print("❌ 找不到crop_bottom_half配置，请检查文件格式")
        success = False

    # 使用正则表达式替换save_step_images配置
    step_pattern = r'"save_step_images":\s*ImageTransformConfig\([^}]+\}[^,]*,?\s*\),'

    if re.search(step_pattern, content, re.DOTALL):
        content = re.sub(step_pattern, step_config, content, flags=re.DOTALL)
    else:
        print("❌ 找不到save_step_images配置，请检查文件格式")
        success = False

    if success:
        # 写回文件
        with open(transforms_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ 已更新配置文件: {transforms_file}")
        return True
    else:
        return False

def main():
    parser = argparse.ArgumentParser(description="启用/禁用图像裁剪变换和步骤图像保存")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--enable", action="store_true", help="启用图像裁剪")
    group.add_argument("--disable", action="store_true", help="禁用图像裁剪")
    group.add_argument("--enable-debug", action="store_true", help="启用图像裁剪+debug模式")
    group.add_argument("--save-steps", action="store_true", help="保存步骤图像(0,10,20,30,40,50)，不进行裁剪")

    args = parser.parse_args()

    if args.enable:
        success = modify_transform_config(enable_crop=True, debug=False, save_steps=False)
    elif args.enable_debug:
        success = modify_transform_config(enable_crop=True, debug=True, save_steps=False)
    elif args.save_steps:
        success = modify_transform_config(enable_crop=False, debug=False, save_steps=True)
    else:  # disable
        success = modify_transform_config(enable_crop=False, debug=False, save_steps=False)
    
    if success:
        print("\n📋 现在可以运行训练命令:")
        print("conda activate lerobot")
        print("python -m lerobot.scripts.train \\")
        print("    --dataset.repo_id=pick_up_parts_less \\")
        print("    --dataset.root=~/data/pick_up_parts_less/lerobot \\")
        print("    --policy.type=act \\")
        print("    --dataset.image_transforms.enable=true \\")
        print("    --dataset.image_transforms.max_num_transforms=1 \\")
        print("    # ... 其他参数")
        
        if args.enable or args.enable_debug:
            print("\n🖼️  训练时会看到: '图像裁剪: 1280x720 -> 1280x360 (去掉上半部分)'")
            if args.enable_debug:
                print("📸 前10张图像的对比图会保存到 debug_crop_images/ 目录")
        elif args.save_steps:
            print("\n📸 训练时会在步骤 0, 10, 20, 30, 40, 50 保存原始图像到 step_images/ 目录")
            print("🖼️  图像不会被裁剪，保持原始尺寸")
    else:
        print("❌ 配置修改失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
