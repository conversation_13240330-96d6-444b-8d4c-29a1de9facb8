# 查看数据集

conda activate lerobot;
python -m lerobot.scripts.visualize_dataset \
    --repo-id=pick_up_parts_less \
    --root=/home/<USER>/data/stock_cloth_2 \
    --episode-index=0

# resume training

conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/stock_cloth_2_training_chunk30_warmup5k_cos_20250723_161437/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=800000 \
    --scheduler.num_decay_steps=800000 


# resume training with data augmentation (从8w步开始启用数据增强)

conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --dataset.image_transforms.random_order=false \
    --wandb.disable_artifact=true

# 恢复

conda activate lerobot
python -m lerobot.scripts.train \
    --config_path=outputs/train/stock_cloth_2_training_chunk30_warmup5k_cos_20250723_161437/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=1000000 \
    --scheduler.type=cosine_decay_with_warmup \
    --scheduler.num_warmup_steps=5000 \
    --scheduler.num_decay_steps=800000 \
    --scheduler.peak_lr=5e-5 \
    --scheduler.decay_lr=1e-6 \
    --save_freq=20000 \
    --log_freq=200 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --eval_freq=0 \
    --policy.device=cuda \
    --wandb.disable_artifact=true \
    --wandb.enable=true \
    --wandb.mode=offline



