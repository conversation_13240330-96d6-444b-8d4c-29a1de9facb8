# 新的训练命令示例

现在你可以直接在训练命令中控制图像变换的调试功能，无需修改配置文件！

## 1. 启用步骤图像保存（保存前 0, 10, 20, 30, 40, 50 步的原始图像，按相机区分）

```bash
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_save_steps=true \
    --dataset.image_transforms.save_steps_dir=step_images \
    --dataset.image_transforms.save_steps_list="[0,10,20,30,40,50]" \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/stock_cloth_2_act_chunk30_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.disable_artifact=true \
    --batch_size=20 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0
```

## 2. 启用图像裁剪

```bash
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_crop=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/stock_cloth_2_act_chunk30_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.disable_artifact=true \
    --batch_size=30 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0
```

## 3. 启用图像裁剪 + 调试模式（保存前10张对比图）

```bash
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_crop=true \
    --dataset.image_transforms.enable_crop_debug=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/stock_cloth_2_act_chunk30_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.disable_artifact=true \
    --batch_size=30 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0
```

## 4. 自定义步骤图像保存（保存前 0, 5, 15, 25 步的图像到自定义目录）

```bash
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.enable_save_steps=true \
    --dataset.image_transforms.save_steps_dir=custom_debug_images \
    --dataset.image_transforms.save_steps_list="[0,5,15,25]" \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/stock_cloth_2_act_chunk30_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.disable_artifact=true \
    --batch_size=20 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=stock_cloth_2_training \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0
```

## 5. 普通训练（不启用任何调试功能）

```bash
conda activate lerobot;
python -m lerobot.scripts.train \
    --dataset.repo_id=pick_up_parts_less \
    --dataset.root=/home/<USER>/data/stock_cloth_2 \
    --policy.type=act \
    --policy.push_to_hub=false \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.video_backend=pyav \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --output_dir=outputs/train/stock_cloth_2_act_chunk30_$(date +%Y%m%d_%H%M%S) \
    --job_name=stock_cloth_2_training \
    --policy.device=cuda \
    --steps=300000 \
    --save_freq=5000 \
    --wandb.disable_artifact=true \
    --batch_size=30 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.project=pick_up_parts_training \
    --policy.n_action_steps=30 \
    --policy.chunk_size=30 \
    --policy.kl_weight=1.0 \
    --policy.optimizer_lr=5e-5 \
    --policy.dropout=0.05 \
    --eval_freq=0
```

## 主要新增参数说明

- `--dataset.image_transforms.enable_crop=true`: 启用图像裁剪（去掉上半部分）
- `--dataset.image_transforms.enable_crop_debug=true`: 启用裁剪调试模式（保存前10张对比图）
- `--dataset.image_transforms.enable_save_steps=true`: 启用步骤图像保存
- `--dataset.image_transforms.save_steps_dir=目录名`: 设置保存图像的目录
- `--dataset.image_transforms.save_steps_list="[0,10,20,30,40,50]"`: 设置要保存图像的步骤

## 输出文件格式

启用步骤图像保存后，图像文件将按以下格式命名：
- `step_000_observation_images_main.png` - 步骤0，主相机
- `step_000_observation_images_wrist.png` - 步骤0，手腕相机
- `step_010_observation_images_main.png` - 步骤10，主相机
- `step_010_observation_images_wrist.png` - 步骤10，手腕相机

这样可以清楚地区分不同相机在不同步骤的图像。

## 优势

1. **无需修改配置文件**: 直接在命令行中控制调试功能
2. **灵活配置**: 可以自定义保存目录和步骤
3. **组合使用**: 可以同时启用多种调试功能
4. **相机区分**: 自动根据相机名称区分保存的图像
5. **向后兼容**: 原有的脚本和配置仍然有效
