#!/usr/bin/env python3
"""
Script to read and analyze pkl file contents in detail
Focus on image data structure and timestamps
"""

import pickle
import os
import sys
import numpy as np
from datetime import datetime

def analyze_data_structure(data, prefix="", max_depth=3, current_depth=0):
    """
    Recursively analyze data structure
    """
    if current_depth > max_depth:
        return
    
    if isinstance(data, dict):
        print(f"{prefix}Dict with {len(data)} keys:")
        for key, value in data.items():
            print(f"{prefix}  '{key}': {type(value)}")
            if isinstance(value, np.ndarray):
                print(f"{prefix}    Shape: {value.shape}, dtype: {value.dtype}")
            elif isinstance(value, (list, tuple)) and len(value) > 0:
                print(f"{prefix}    Length: {len(value)}, first item type: {type(value[0])}")
            elif isinstance(value, (int, float, str)):
                print(f"{prefix}    Value: {value}")
            
            # Recurse for nested structures
            if isinstance(value, (dict, list)) and current_depth < max_depth:
                analyze_data_structure(value, prefix + "    ", max_depth, current_depth + 1)
    
    elif isinstance(data, (list, tuple)):
        print(f"{prefix}List/Tuple with {len(data)} items:")
        if len(data) > 0:
            print(f"{prefix}  First item type: {type(data[0])}")
            if len(data) <= 5:  # Show details for small lists
                for i, item in enumerate(data):
                    print(f"{prefix}  [{i}]: {type(item)}")
                    if isinstance(item, np.ndarray):
                        print(f"{prefix}      Shape: {item.shape}, dtype: {item.dtype}")
            
            # Recurse for first item if it's a complex structure
            if isinstance(data[0], (dict, list)) and current_depth < max_depth:
                print(f"{prefix}  Analyzing first item:")
                analyze_data_structure(data[0], prefix + "    ", max_depth, current_depth + 1)
    
    elif isinstance(data, np.ndarray):
        print(f"{prefix}NumPy array: shape={data.shape}, dtype={data.dtype}")
        if data.size < 20:  # Show small arrays
            print(f"{prefix}  Values: {data}")


def check_image_data(data, path_info=""):
    """
    Specifically check for image data patterns
    """
    print(f"\n🔍 Image Data Analysis {path_info}")
    print("=" * 50)

    image_info = {}

    def find_images(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key

                # Check if this looks like image data
                if 'image' in key.lower() or 'camera' in key.lower() or 'rgb' in key.lower():
                    print(f"📷 Found potential image key: {current_path}")
                    if isinstance(value, np.ndarray):
                        print(f"   Shape: {value.shape}, dtype: {value.dtype}")
                        if len(value.shape) == 3 and value.shape[2] in [3, 4]:
                            print(f"   ✅ Looks like RGB/RGBA image: {value.shape}")
                            image_info[current_path] = {
                                'shape': value.shape,
                                'dtype': value.dtype,
                                'size_mb': value.nbytes / (1024*1024)
                            }
                        elif len(value.shape) == 1:
                            print(f"   🤔 Might be compressed image data: {value.shape}")
                            # Try to decode as JPEG/PNG
                            try:
                                import cv2
                                decoded = cv2.imdecode(value, cv2.IMREAD_COLOR)
                                if decoded is not None:
                                    print(f"   ✅ Successfully decoded to: {decoded.shape}")
                                    image_info[current_path] = {
                                        'shape': value.shape,
                                        'decoded_shape': decoded.shape,
                                        'dtype': value.dtype,
                                        'size_mb': value.nbytes / (1024*1024),
                                        'note': 'compressed image (JPEG/PNG)'
                                    }
                                else:
                                    print(f"   ❌ Could not decode as standard image format")
                            except:
                                print(f"   ❌ Could not decode as image")

                            image_info[current_path] = {
                                'shape': value.shape,
                                'dtype': value.dtype,
                                'size_mb': value.nbytes / (1024*1024),
                                'note': 'possibly compressed'
                            }
                    elif isinstance(value, dict):
                        print(f"   📁 Image dict with keys: {list(value.keys())}")
                        # Analyze each camera in the images dict
                        for cam_key, cam_value in value.items():
                            if isinstance(cam_value, np.ndarray):
                                print(f"     📷 Camera '{cam_key}': shape={cam_value.shape}, dtype={cam_value.dtype}")
                                if len(cam_value.shape) == 1:
                                    print(f"       🤔 1D array - likely compressed image")
                                    # Try to decode
                                    try:
                                        import cv2
                                        decoded = cv2.imdecode(cam_value, cv2.IMREAD_COLOR)
                                        if decoded is not None:
                                            print(f"       ✅ Decoded to: {decoded.shape}")
                                            image_info[f"{current_path}.{cam_key}"] = {
                                                'shape': cam_value.shape,
                                                'decoded_shape': decoded.shape,
                                                'dtype': cam_value.dtype,
                                                'size_mb': cam_value.nbytes / (1024*1024),
                                                'note': 'compressed image'
                                            }
                                        else:
                                            print(f"       ❌ Could not decode")
                                    except Exception as e:
                                        print(f"       ❌ Decode error: {e}")
                                elif len(cam_value.shape) == 3:
                                    print(f"       ✅ 3D array - likely raw RGB image")
                                    image_info[f"{current_path}.{cam_key}"] = {
                                        'shape': cam_value.shape,
                                        'dtype': cam_value.dtype,
                                        'size_mb': cam_value.nbytes / (1024*1024),
                                        'note': 'raw RGB image'
                                    }
                        find_images(value, current_path)

                # Recurse into nested structures
                elif isinstance(value, dict):
                    find_images(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_images(value[0], f"{current_path}[0]")

    find_images(data)
    return image_info


def check_timestamp_data(data, path_info=""):
    """
    Check for timestamp information
    """
    print(f"\n⏰ Timestamp Analysis {path_info}")
    print("=" * 50)
    
    timestamp_info = {}
    
    def find_timestamps(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key
                
                # Check if this looks like timestamp data
                if any(word in key.lower() for word in ['time', 'stamp', 'date']):
                    print(f"⏰ Found potential timestamp key: {current_path}")
                    print(f"   Type: {type(value)}, Value: {value}")
                    timestamp_info[current_path] = {
                        'type': str(type(value)),
                        'value': value
                    }
                
                # Recurse into nested structures
                if isinstance(value, dict):
                    find_timestamps(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_timestamps(value[0], f"{current_path}[0]")
    
    find_timestamps(data)
    return timestamp_info


def check_arm_joints_data(data, path_info=""):
    """
    Check for arm_joints information
    """
    print(f"\n🦾 Arm Joints Analysis {path_info}")
    print("=" * 50)
    
    arm_joints_info = {}
    
    def find_arm_joints(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key
                
                # Check if this looks like arm_joints data
                if 'arm' in key.lower() or 'joint' in key.lower() or key.lower() == 'arm_joints':
                    print(f"🦾 Found potential arm joints key: {current_path}")
                    print(f"   Type: {type(value)}")
                    
                    if isinstance(value, np.ndarray):
                        print(f"   Shape: {value.shape}, dtype: {value.dtype}")
                        print(f"   Values: {value}")
                        arm_joints_info[current_path] = {
                            'type': str(type(value)),
                            'shape': value.shape,
                            'dtype': value.dtype,
                            'values': value.tolist() if value.size < 20 else "Array too large to display"
                        }
                    else:
                        print(f"   Value: {value}")
                        arm_joints_info[current_path] = {
                            'type': str(type(value)),
                            'value': value
                        }
                
                # Recurse into nested structures
                if isinstance(value, dict):
                    find_arm_joints(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_arm_joints(value[0], f"{current_path}[0]")
    
    find_arm_joints(data)
    return arm_joints_info


def analyze_pkl_file(pkl_path):
    """
    Comprehensive analysis of a pkl file
    """
    print(f"📄 Analyzing PKL file: {os.path.basename(pkl_path)}")
    print("=" * 80)
    
    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"✅ Successfully loaded pkl file")
        print(f"📊 Root data type: {type(data)}")
        
        if isinstance(data, dict):
            print(f"📋 Root keys: {list(data.keys())}")
        elif isinstance(data, (list, tuple)):
            print(f"📋 Root list length: {len(data)}")
        
        print(f"💾 File size: {os.path.getsize(pkl_path) / (1024*1024):.2f} MB")
        
        # Detailed structure analysis
        print(f"\n🏗️ Data Structure Analysis")
        print("=" * 50)
        analyze_data_structure(data)
        
        # Image data analysis
        image_info = check_image_data(data, f"({os.path.basename(pkl_path)})")
        
        # Timestamp analysis
        timestamp_info = check_timestamp_data(data, f"({os.path.basename(pkl_path)})")
        
        # Arm joints analysis
        arm_joints_info = check_arm_joints_data(data, f"({os.path.basename(pkl_path)})")
        
        # Summary
        print(f"\n📋 Summary for {os.path.basename(pkl_path)}")
        print("=" * 50)
        print(f"🖼️  Found {len(image_info)} potential image data entries")
        for path, info in image_info.items():
            print(f"   {path}: {info['shape']} ({info['size_mb']:.2f} MB)")
        
        print(f"⏰ Found {len(timestamp_info)} potential timestamp entries")
        for path, info in timestamp_info.items():
            print(f"   {path}: {info['value']}")
        
        print(f"🦾 Found {len(arm_joints_info)} potential arm joints entries")
        for path, info in arm_joints_info.items():
            print(f"   {path}: {info['values'] if 'values' in info else info['value']}")
        
        return {
            'images': image_info,
            'timestamps': timestamp_info,
            'arm_joints': arm_joints_info,
            'root_type': type(data),
            'file_size_mb': os.path.getsize(pkl_path) / (1024*1024)
        }
        
    except Exception as e:
        print(f"❌ Error loading pkl file: {e}")
        return None


def main():
    if len(sys.argv) < 2:
        print("Usage: python read_print_pkl.py <pkl_file_or_directory>")
        print("Examples:")
        print("  python read_print_pkl.py /path/to/file.pkl")
        print("  python read_print_pkl.py /path/to/pkl_directory")
        return
    
    input_path = sys.argv[1]
    
    if os.path.isfile(input_path) and input_path.endswith('.pkl'):
        # Single file analysis
        analyze_pkl_file(input_path)
    
    elif os.path.isdir(input_path):
        # Directory analysis - analyze first few files
        pkl_files = [f for f in os.listdir(input_path) if f.endswith('.pkl')]
        pkl_files.sort()
        
        if not pkl_files:
            print(f"❌ No pkl files found in {input_path}")
            return
        
        print(f"📁 Found {len(pkl_files)} pkl files in directory")
        print(f"🔍 Analyzing first 3 files for structure comparison...")
        
        all_results = {}
        for i, pkl_file in enumerate(pkl_files[:6]):
            pkl_path = os.path.join(input_path, pkl_file)
            print(f"\n{'='*80}")
            print(f"FILE {i+1}/{min(6, len(pkl_files))}")
            result = analyze_pkl_file(pkl_path)
            if result:
                all_results[pkl_file] = result
        
        # Compare structures across files
        print(f"\n🔄 Cross-file Comparison")
        print("=" * 80)
        
        if all_results:
            # Compare image structures
            print("🖼️  Image Data Consistency:")
            first_file = list(all_results.keys())[0]
            first_images = all_results[first_file]['images']
            
            for file_name, result in all_results.items():
                images = result['images']
                print(f"   {file_name}: {len(images)} image entries")
                for path, info in images.items():
                    print(f"     {path}: {info['shape']}")
            
            # Compare timestamp structures
            print("\n⏰ Timestamp Data Consistency:")
            for file_name, result in all_results.items():
                timestamps = result['timestamps']
                print(f"   {file_name}: {len(timestamps)} timestamp entries")
                for path, info in timestamps.items():
                    print(f"     {path}: {info['value']}")
    
    else:
        print(f"❌ Invalid path: {input_path}")


if __name__ == "__main__":
    main()
