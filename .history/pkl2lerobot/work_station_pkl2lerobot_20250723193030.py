#!/usr/bin/env python3
"""
PKL to Training Format Converter (新数据格式专用版本)
仅支持新数据格式（单摄像头），输出训练专用的字段格式：
- base_0_rgb: 主相机图像（从 images['camera'] 获取）
- observation.state: 16维机器人状态（左臂8维+右臂8维，每臂7关节+1夹爪）
- action: 16维控制信号（左臂8维+右臂8维，每臂7关节+1夹爪）

新数据格式结构：
- arm_joints: {'robot': [14维关节数据]} -> 重组为16维（左臂7关节+左夹爪+右臂7关节+右夹爪）
- control: {'arm_joints': {'robot': [14维控制数据]}} -> 重组为16维
- hand_joints: {'left': [1维], 'right': [1维]} -> 填充到对应夹爪位置
- images: {'camera': 压缩图像数据}

注意：新数据格式只有一个摄像头，因此只输出 base_0_rgb 字段。
状态组织：[左臂7关节, 左夹爪, 右臂7关节, 右夹爪] = 16维
"""

import numpy as np
import pickle
import re
import os
import argparse
import cv2
import io
import shutil
import tempfile
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

import logging
import sys
import datasets
from datasets import concatenate_datasets
import json
from lerobot.datasets.lerobot_dataset import LeRobotDataset
from lerobot.datasets.utils import embed_images, DEFAULT_CHUNK_SIZE

'''
使用方法： 

conda activate lerobot && python pkl2lerobot/work_station_pkl2lerobot.py \
    /home/<USER>/Downloads/0716_201330 \
    "pick_up_parts_less" \
    --repo_id   "pick_up_parts_less"\
    --overwrite --original_resolution  --num_worker 20

'''


# ============================================================================
# 配置参数 - 训练格式专用
# ============================================================================

# 数据集配置
DEFAULT_FPS = 20  # 数据集fps（用于元数据，很重要）
DEFAULT_IMAGE_SIZE = (224, 224)  # 标准图像尺寸 (height, width)
DEFAULT_ROBOT_TYPE = "tienkung"  # 默认机器人类型



# 新数据格式只有一个摄像头 'camera'，映射到 base_0_rgb

# 机器人配置 - 双臂机器人
ARM_JOINTS_DIM = 16  # 双臂总维度：左臂8维(7关节+1夹爪) + 右臂8维(7关节+1夹爪)
SINGLE_ARM_DIM = 8   # 单臂维度：7关节 + 1夹爪
LEFT_ARM_JOINTS = 7  # 左臂关节数
RIGHT_ARM_JOINTS = 7 # 右臂关节数

# 处理配置
DEFAULT_NUM_WORKERS = 20
DEFAULT_USE_PROCESSES = True

# 相机内参配置 - 用于depth到点云转换
# 请根据你的实际相机参数修改这些值
DEFAULT_CAMERA_INTRINSICS = {
    'fx': 640.0,  # 焦距x
    'fy': 640.0,  # 焦距y
    'cx': 320.0,  # 主点x
    'cy': 240.0,  # 主点y
    'depth_scale': 1000.0,  # depth值的缩放因子，通常depth以毫米为单位，需要转换为米
}

# ============================================================================
# 使用标准的LeRobot数据集类，不需要自定义修复
# ============================================================================

# ============================================================================
# 辅助函数
# ============================================================================

def depth_to_pointcloud(depth_image, camera_intrinsics=None):
    """
    将depth图像转换为点云

    Args:
        depth_image: numpy数组，depth图像 (H, W)
        camera_intrinsics: 相机内参字典，包含fx, fy, cx, cy, depth_scale

    Returns:
        numpy数组: 点云数据 (N, 3)，其中N是有效点的数量，3表示(x, y, z)坐标
    """
    if camera_intrinsics is None:
        camera_intrinsics = DEFAULT_CAMERA_INTRINSICS

    # 获取相机内参
    fx = camera_intrinsics['fx']
    fy = camera_intrinsics['fy']
    cx = camera_intrinsics['cx']
    cy = camera_intrinsics['cy']
    depth_scale = camera_intrinsics['depth_scale']

    # 获取图像尺寸
    height, width = depth_image.shape

    # 创建像素坐标网格
    u, v = np.meshgrid(np.arange(width), np.arange(height))

    # 将depth图像转换为米为单位（如果原始单位是毫米）
    depth_m = depth_image.astype(np.float32) / depth_scale

    # 过滤掉无效的depth值（通常是0或非常大的值）
    valid_mask = (depth_m > 0) & (depth_m < 10.0)  # 假设有效深度范围是0-10米

    # 提取有效的像素坐标和深度值
    u_valid = u[valid_mask]
    v_valid = v[valid_mask]
    depth_valid = depth_m[valid_mask]

    # 将像素坐标转换为3D坐标
    x = (u_valid - cx) * depth_valid / fx
    y = (v_valid - cy) * depth_valid / fy
    z = depth_valid

    # 组合成点云 (N, 3)
    pointcloud = np.column_stack((x, y, z))

    return pointcloud

def setup_logging(input_dir):
    """设置日志记录"""
    # 确保logs目录存在
    logs_dir = os.path.join(input_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)

    log_file = os.path.join(logs_dir, f"pkl2openpi_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logging.info(f"日志文件: {log_file}")
    return log_file

def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳"""
    # 匹配多种时间戳格式（按优先级排序）
    patterns = [
        r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+)',  # ISO格式：2025-06-16T18:14:56.348157
        r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})',       # YYYY-MM-DD_HH-MM-SS
        r'(\d{13})',  # 13位毫秒时间戳
        r'(\d{10})',  # 10位秒时间戳
        # 注意：浮点数时间戳放在最后，避免误匹配ISO格式中的小数部分
    ]

    for pattern in patterns:
        match = re.search(pattern, str(filename))
        if match:
            timestamp_str = match.group(1)
            try:
                if 'T' in timestamp_str:
                    # ISO格式：2025-06-16T18:14:56.348157
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%dT%H:%M:%S.%f')
                    return dt.timestamp()
                elif '-' in timestamp_str and '_' in timestamp_str:
                    # 日期时间格式：YYYY-MM-DD_HH-MM-SS
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d_%H-%M-%S')
                    return dt.timestamp()
                # 移除了浮点数时间戳处理，避免误匹配ISO格式
                elif len(timestamp_str) == 13:
                    # 毫秒时间戳
                    return int(timestamp_str) / 1000.0
                elif len(timestamp_str) == 10:
                    # 秒时间戳
                    return int(timestamp_str)
            except (ValueError, OverflowError):
                continue

    return None



def process_pkl_file(pkl_path, task="manipulation", frame_index=0, use_original_resolution=False,
                     camera_intrinsics=None, convert_depth_to_pointcloud=False):
    """
    处理单个PKL文件，提取训练格式数据（仅支持新数据格式）

    Args:
        pkl_path: PKL文件路径
        task: 任务名称
        frame_index: 帧索引，用于生成递增时间戳
        use_original_resolution: 是否使用原始分辨率（不resize）
        camera_intrinsics: 相机内参字典，用于depth到点云转换
        convert_depth_to_pointcloud: 是否将depth转换为点云

    Returns:
        dict: 包含训练格式字段的数据
    """
    logging.info(f"处理PKL文件: {pkl_path}")
    
    # 加载PKL文件
    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
    except Exception as e:
        logging.error(f"无法加载PKL文件 {pkl_path}: {e}")
        return None
    
    # 使用基于FPS的简单递增时间戳，避免复杂的时间戳解析问题
    simple_timestamp = frame_index / DEFAULT_FPS
    logging.debug(f"使用简单时间戳: frame_index={frame_index}, timestamp={simple_timestamp:.3f}s")
    
    # 提取机器人状态数据 - 16维双臂状态（左臂8维+右臂8维）
    arm_joints = np.zeros(ARM_JOINTS_DIM, dtype=np.float32)  # 双臂16维：左臂8维(7关节+1夹爪) + 右臂8维(7关节+1夹爪)

    if 'arm_joints' in data:
        logging.info(f"找到arm_joints数据")
        if isinstance(data['arm_joints'], dict):
            # 新数据格式：{'robot': [14维关节数据]}
            if 'robot' in data['arm_joints']:
                robot_joints = data['arm_joints']['robot']
                if len(robot_joints) >= 14:
                    # 原始数据是14维：左臂7维 + 右臂7维
                    # 重新组织为：左臂7关节 + 左夹爪 + 右臂7关节 + 右夹爪
                    left_arm_joints = robot_joints[:7].astype(np.float32)   # 左臂7个关节
                    right_arm_joints = robot_joints[7:14].astype(np.float32) # 右臂7个关节

                    # 组织新的16维状态：左臂7关节 + 左夹爪 + 右臂7关节 + 右夹爪
                    arm_joints[:7] = left_arm_joints      # 左臂7个关节 (索引0-6)
                    arm_joints[7] = 0.0                   # 左夹爪 (索引7，稍后从hand_joints填充)
                    arm_joints[8:15] = right_arm_joints   # 右臂7个关节 (索引8-14)
                    arm_joints[15] = 0.0                  # 右夹爪 (索引15，稍后从hand_joints填充)

                    logging.info(f"重组机器人关节数据: 左臂{left_arm_joints}, 右臂{right_arm_joints}")
                else:
                    logging.warning(f"机器人关节数据维度不足: {len(robot_joints)} < 14")
            else:
                logging.warning(f"新数据格式中未找到'robot'键，跳过arm_joints处理")
        elif isinstance(data['arm_joints'], (list, np.ndarray)):
            # 数组格式：[左臂7维, 右臂7维]
            joints_array = np.array(data['arm_joints'])
            if len(joints_array) >= 14:
                # 重新组织为16维格式
                left_arm_joints = joints_array[:7].astype(np.float32)
                right_arm_joints = joints_array[7:14].astype(np.float32)

                arm_joints[:7] = left_arm_joints      # 左臂7个关节
                arm_joints[7] = 0.0                   # 左夹爪
                arm_joints[8:15] = right_arm_joints   # 右臂7个关节
                arm_joints[15] = 0.0                  # 右夹爪
            logging.info(f"重组关节数据: {arm_joints}")
    else:
        logging.info(f"文件中没有arm_joints数据")

    # 处理手部关节数据 (填充到对应的夹爪位置)
    if 'hand_joints' in data:
        logging.info(f"找到hand_joints数据")
        hand_data = data['hand_joints']
        if isinstance(hand_data, dict):
            left_hand = hand_data.get('left', np.array([0.0]))
            right_hand = hand_data.get('right', np.array([0.0]))

            # 填充夹爪数据到新的位置
            if len(left_hand) > 0:
                arm_joints[7] = float(left_hand[0])   # 左夹爪位置 (索引7)
                logging.info(f"设置左臂夹爪数据: {arm_joints[7]}")
            if len(right_hand) > 0:
                arm_joints[15] = float(right_hand[0]) # 右夹爪位置 (索引15)
                logging.info(f"设置右臂夹爪数据: {arm_joints[15]}")

            logging.info(f"手部关节数据 - 左: {left_hand}, 右: {right_hand}")

    # 提取控制数据作为动作 - 16维双臂动作（左臂8维+右臂8维）
    control_joints = np.zeros(ARM_JOINTS_DIM, dtype=np.float32)  # 双臂16维：左臂8维(7关节+1夹爪) + 右臂8维(7关节+1夹爪)

    if 'control' in data and 'arm_joints' in data['control']:
        logging.info(f"找到control.arm_joints数据")
        control_data = data['control']['arm_joints']
        if isinstance(control_data, dict):
            # 新数据格式：{'robot': [14维关节数据]}
            if 'robot' in control_data:
                robot_control = control_data['robot']
                if len(robot_control) >= 14:
                    # 原始控制数据是14维：左臂7维 + 右臂7维
                    # 重新组织为：左臂7关节 + 左夹爪 + 右臂7关节 + 右夹爪
                    left_arm_control = robot_control[:7].astype(np.float32)   # 左臂7个关节控制
                    right_arm_control = robot_control[7:14].astype(np.float32) # 右臂7个关节控制

                    # 组织新的16维控制：左臂7关节 + 左夹爪 + 右臂7关节 + 右夹爪
                    control_joints[:7] = left_arm_control      # 左臂7个关节控制 (索引0-6)
                    control_joints[7] = 0.0                    # 左夹爪控制 (索引7，稍后从hand_joints填充)
                    control_joints[8:15] = right_arm_control   # 右臂7个关节控制 (索引8-14)
                    control_joints[15] = 0.0                   # 右夹爪控制 (索引15，稍后从hand_joints填充)

                    logging.info(f"重组机器人控制数据: 左臂{left_arm_control}, 右臂{right_arm_control}")
                else:
                    logging.warning(f"机器人控制数据维度不足: {len(robot_control)} < 14")
            else:
                logging.warning(f"新数据格式中未找到'robot'键，跳过control.arm_joints处理")
        elif isinstance(control_data, (list, np.ndarray)):
            # 数组格式：[左臂7维, 右臂7维]
            control_array = np.array(control_data)
            if len(control_array) >= 14:
                # 重新组织为16维格式
                left_arm_control = control_array[:7].astype(np.float32)
                right_arm_control = control_array[7:14].astype(np.float32)

                control_joints[:7] = left_arm_control      # 左臂7个关节控制
                control_joints[7] = 0.0                    # 左夹爪控制
                control_joints[8:15] = right_arm_control   # 右臂7个关节控制
                control_joints[15] = 0.0                   # 右夹爪控制
            logging.info(f"重组双臂动作: {control_joints}")
    else:
        logging.info(f"文件中没有control.arm_joints数据")

    # 处理控制手部关节数据 (填充到对应的夹爪控制位置)
    if 'control' in data and 'hand_joints' in data['control']:
        logging.info(f"找到control.hand_joints数据")
        hand_control_data = data['control']['hand_joints']
        if isinstance(hand_control_data, dict):
            left_hand_control = hand_control_data.get('left', np.array([0.0]))
            right_hand_control = hand_control_data.get('right', np.array([0.0]))

            # 填充夹爪控制数据到新的位置
            if len(left_hand_control) > 0:
                control_joints[7] = float(left_hand_control[0])   # 左夹爪控制位置 (索引7)
                logging.info(f"设置左臂夹爪控制数据: {control_joints[7]}")
            if len(right_hand_control) > 0:
                control_joints[15] = float(right_hand_control[0]) # 右夹爪控制位置 (索引15)
                logging.info(f"设置右臂夹爪控制数据: {control_joints[15]}")

            logging.info(f"手部控制数据 - 左: {left_hand_control}, 右: {right_hand_control}")

    # 加载图像数据并转换为numpy数组（仅支持新数据格式的单摄像头）
    images = {}

    # 从PKL文件中提取压缩图像数据（仅支持新数据格式）
    if 'images' in data:
        # 处理所有图像数据（RGB和depth）
        for img_key, img_data in data['images'].items():
            is_depth = 'depth' in img_key.lower()

            if isinstance(img_data, np.ndarray) and img_data.ndim == 1:
                # 压缩图像数据，使用OpenCV解码
                try:
                    # 使用OpenCV解码
                    img_array = img_data.astype(np.uint8)
                    if is_depth:
                        # 对于depth数据，尝试不同的解码方式
                        img_cv = cv2.imdecode(img_array, cv2.IMREAD_UNCHANGED)
                        if img_cv is None:
                            img_cv = cv2.imdecode(img_array, cv2.IMREAD_GRAYSCALE)
                    else:
                        img_cv = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

                    if img_cv is not None:
                        if is_depth:
                            # 处理depth数据
                            if convert_depth_to_pointcloud:
                                # 转换为点云
                                pointcloud = depth_to_pointcloud(img_cv, camera_intrinsics)
                                images[f'observation.pointcloud.{img_key}'] = pointcloud
                                logging.info(f"从PKL解码{img_key}深度图像并转换为点云: {img_cv.shape} -> {pointcloud.shape}")
                            else:
                                # 保存原始depth图像
                                if use_original_resolution:
                                    final_img = img_cv
                                else:
                                    final_img = cv2.resize(img_cv, (224, 224), interpolation=cv2.INTER_NEAREST)
                                images[f'observation.images.{img_key}'] = final_img
                                logging.info(f"从PKL解码{img_key}深度图像: {img_cv.shape} -> {final_img.shape}")
                        else:
                            # 处理RGB数据
                            if use_original_resolution:
                                final_img = img_cv
                                logging.info(f"从PKL解码{img_key}图像: {img_cv.shape}, 使用原始分辨率, dtype={final_img.dtype}")
                            else:
                                final_img = cv2.resize(img_cv, (224, 224), interpolation=cv2.INTER_LANCZOS4)
                                logging.info(f"从PKL解码{img_key}图像: {img_cv.shape} -> resize到{final_img.shape}, dtype={final_img.dtype}")

                            # 将主相机图像映射到observation.images.main（ACT模型期望的格式）
                            if img_key == 'camera':
                                images['observation.images.main'] = final_img
                            else:
                                images[f'observation.images.{img_key}'] = final_img
                    else:
                        logging.warning(f"OpenCV解码失败，{img_key}图像数据可能损坏")
                except Exception as e:
                    logging.warning(f"解码PKL中的{img_key}图像时出错: {e}")
            elif isinstance(img_data, np.ndarray) and img_data.ndim >= 2:
                # 已解码的图像数据
                try:
                    if is_depth:
                        # 处理depth数据
                        if img_data.ndim == 3 and img_data.shape[2] == 3:
                            # 如果是3通道的depth图像，转换为单通道
                            img_cv = cv2.cvtColor(img_data.astype(np.uint8), cv2.COLOR_BGR2GRAY)
                        else:
                            img_cv = img_data.astype(np.float32) if img_data.dtype != np.float32 else img_data

                        if convert_depth_to_pointcloud:
                            # 转换为点云
                            pointcloud = depth_to_pointcloud(img_cv, camera_intrinsics)
                            images[f'observation.pointcloud.{img_key}'] = pointcloud
                            logging.info(f"使用PKL中的{img_key}深度图像并转换为点云: {img_data.shape} -> {pointcloud.shape}")
                        else:
                            # 保存原始depth图像
                            if use_original_resolution:
                                final_img = img_cv
                            else:
                                final_img = cv2.resize(img_cv, (224, 224), interpolation=cv2.INTER_NEAREST)
                            images[f'observation.images.{img_key}'] = final_img
                            logging.info(f"使用PKL中的{img_key}深度图像: {img_data.shape} -> {final_img.shape}")
                    else:
                        # 处理RGB数据
                        img_cv = img_data.astype(np.uint8)
                        if use_original_resolution:
                            final_img = img_cv
                            logging.info(f"使用PKL中的{img_key}图像: {img_data.shape}, 使用原始分辨率, dtype={final_img.dtype}")
                        else:
                            final_img = cv2.resize(img_cv, (224, 224), interpolation=cv2.INTER_LANCZOS4)
                            logging.info(f"使用PKL中的{img_key}图像: {img_data.shape} -> resize到{final_img.shape}, dtype={final_img.dtype}")

                        # 将主相机图像映射到observation.images.main（ACT模型期望的格式）
                        if img_key == 'camera':
                            images['observation.images.main'] = final_img
                        else:
                            images[f'observation.images.{img_key}'] = final_img
                except Exception as e:
                    logging.warning(f"处理PKL中的{img_key}图像时出错: {e}")
            else:
                logging.warning(f"PKL中的{img_key}图像数据格式不支持: {type(img_data)}, shape: {getattr(img_data, 'shape', 'N/A')}")
    else:
        logging.warning(f"PKL文件中未找到'images'键，跳过图像处理")


    # 如果没有任何图像，创建默认的黑色图像
    if not images:
        logging.warning(f"帧不包含任何图像数据，使用默认黑色图像")
        if use_original_resolution:
            # 使用一个合理的默认分辨率
            default_image = np.zeros((720, 1280, 3), dtype=np.uint8)  # 常见的720p分辨率
            logging.info(f"使用原始分辨率模式，创建默认图像: {default_image.shape}")
        else:
            default_image = np.zeros((224, 224, 3), dtype=np.uint8)
            logging.info(f"使用resize模式，创建默认图像: {default_image.shape}")

        images = {
            'observation.images.main': default_image
        }

    # 创建训练格式的帧数据（LeRobot会自动生成时间戳）
    frame = {
        "task": task,                       # 任务标识符 - LeRobot要求的字段
        "action": control_joints,           # 控制信号作为Ground Truth
        "observation.state": arm_joints,    # 当前机器人状态作为输入
        **images                           # 图像数据作为输入
    }

    return frame

def verify_time_order(pkl_files):
    """验证PKL文件是否按时间顺序排列"""
    timestamps = []
    for pkl_file in pkl_files:
        timestamp = extract_timestamp_from_filename(pkl_file.name)
        if timestamp is not None:
            timestamps.append((pkl_file.name, timestamp))

    # 检查是否按时间顺序排列
    if len(timestamps) > 1:
        is_sorted = all(timestamps[i][1] <= timestamps[i+1][1] for i in range(len(timestamps)-1))
        if not is_sorted:
            logging.warning("⚠️  PKL文件可能不是按时间顺序排列的！")
            logging.warning("前几个文件的时间戳:")
            for name, ts in timestamps[:5]:
                logging.warning(f"  {name}: {ts}")
        else:
            logging.info("✅ PKL文件按时间顺序正确排列")
            logging.info(f"时间范围: {timestamps[0][1]} -> {timestamps[-1][1]}")

    return timestamps

def create_training_dataset(input_dir, output_dir, repo_id="training_dataset", robot_type=DEFAULT_ROBOT_TYPE,
                          task="manipulation", overwrite=False, use_original_resolution=False,
                          camera_intrinsics=None, convert_depth_to_pointcloud=False):
    """
    创建训练格式的LeRobot数据集

    Args:
        input_dir: 输入目录，包含PKL文件
        output_dir: 输出目录
        repo_id: 数据集ID
        robot_type: 机器人类型
        task: 任务名称
        overwrite: 是否覆盖现有文件
        use_original_resolution: 是否使用原始分辨率（不resize）
        camera_intrinsics: 相机内参字典，用于depth到点云转换
        convert_depth_to_pointcloud: 是否将depth转换为点云
    """
    logging.info(f"开始创建训练格式数据集...")
    logging.info(f"输入目录: {input_dir}")
    logging.info(f"输出目录: {output_dir}")
    logging.info(f"图像分辨率模式: {'原始分辨率' if use_original_resolution else 'resize到224x224'}")

    # 先处理一个PKL文件来获取实际的图像尺寸
    pkl_files = list(Path(input_dir).rglob("*.pkl"))
    if not pkl_files:
        raise ValueError(f"在目录 {input_dir} 中未找到PKL文件")

    # 从第一个PKL文件获取图像尺寸
    sample_frame = process_pkl_file(pkl_files[0], task=task, frame_index=0,
                                  use_original_resolution=use_original_resolution,
                                  camera_intrinsics=camera_intrinsics,
                                  convert_depth_to_pointcloud=convert_depth_to_pointcloud)
    if sample_frame is None:
        raise ValueError(f"无法处理样本PKL文件: {pkl_files[0]}")

    # 获取实际图像尺寸
    img_height, img_width = None, None
    if 'observation.images.main' in sample_frame:
        img_height, img_width = sample_frame['observation.images.main'].shape[:2]

    if img_height is None or img_width is None:
        # 如果没有图像，使用默认尺寸
        img_height, img_width = DEFAULT_IMAGE_SIZE
        logging.warning("未找到图像数据，使用默认图像尺寸")
    else:
        logging.info(f"检测到图像尺寸: {img_height}x{img_width}")

    # 动态构建特征定义，根据样本帧的内容
    features = {
        # 动作：16维双臂动作（左臂8维(7关节+1夹爪) + 右臂8维(7关节+1夹爪)）
        "action": {
            "dtype": "float32",
            "shape": (ARM_JOINTS_DIM,),  # 双臂：左臂8维 + 右臂8维
            "names": ([f"left_joint_{i+1}" for i in range(LEFT_ARM_JOINTS)] + ["left_gripper"] +
                     [f"right_joint_{i+1}" for i in range(RIGHT_ARM_JOINTS)] + ["right_gripper"])
        },
        # 机器人状态：16维（左臂8维(7关节+1夹爪) + 右臂8维(7关节+1夹爪)）
        "observation.state": {
            "dtype": "float32",
            "shape": (ARM_JOINTS_DIM,),  # 左臂8维 + 右臂8维
            "names": ([f"left_joint_{i+1}" for i in range(LEFT_ARM_JOINTS)] + ["left_gripper"] +
                     [f"right_joint_{i+1}" for i in range(RIGHT_ARM_JOINTS)] + ["right_gripper"])
        }
    }

    # 根据样本帧动态添加图像和点云特征
    for key, value in sample_frame.items():
        if key.startswith('observation.images.'):
            # 图像特征
            if isinstance(value, np.ndarray) and value.ndim == 3:
                features[key] = {
                    "dtype": "image",
                    "shape": value.shape,
                    "names": ["height", "width", "channels"]
                }
                logging.info(f"添加图像特征: {key}, shape: {value.shape}")
        elif key.startswith('observation.pointcloud.'):
            # 点云特征
            if isinstance(value, np.ndarray) and value.ndim == 2:
                features[key] = {
                    "dtype": "float32",
                    "shape": (None, 3),  # 动态点数，3D坐标
                    "names": ["x", "y", "z"]
                }
                logging.info(f"添加点云特征: {key}, shape: {value.shape}")

    # 确保至少有一个主图像特征
    if 'observation.images.main' not in features:
        # 如果没有主图像，创建默认的
        features["observation.images.main"] = {
            "dtype": "image",
            "shape": (img_height, img_width, 3),
            "names": ["height", "width", "channels"]
        }
        logging.info(f"添加默认主图像特征: shape: ({img_height}, {img_width}, 3)")

    # 处理目录问题
    final_output_path = Path(output_dir)

    # 检查目标目录是否存在
    if final_output_path.exists() and not overwrite:
        raise FileExistsError(f"目录已存在: {output_dir}。使用 --overwrite 选项覆盖现有文件。")

    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_output_path = Path(temp_dir) / "training_dataset"

        logging.info(f"使用临时目录: {temp_output_path}")

        # 创建训练格式的LeRobot数据集
        fps = DEFAULT_FPS  # 仅用于数据集元数据，实际处理所有帧不做抽帧
        dataset = LeRobotDataset.create(
            repo_id=repo_id,
            fps=fps,
            features=features,
            robot_type=robot_type,  # 使用传入的机器人类型
            use_videos=False,  # 使用图像而不是视频
            root=str(temp_output_path)
        )

        # 获取所有子目录（每个子目录是一个episode），排除特殊目录
        exclude_dirs = {'logs', 'openpi', 'images', 'videos', '__pycache__', '.git'}
        episode_dirs = [d for d in Path(input_dir).iterdir()
                       if d.is_dir() and d.name not in exclude_dirs]

        # 如果没有有效的子目录，检查当前目录是否有PKL文件
        if not episode_dirs:
            pkl_files = list(Path(input_dir).glob("*.pkl"))
            if pkl_files:
                episode_dirs = [Path(input_dir)]  # 当前目录作为一个episode
                logging.info(f"在主目录中找到 {len(pkl_files)} 个PKL文件，将主目录作为一个episode")
            else:
                raise ValueError(f"在目录 {input_dir} 中未找到PKL文件或有效的episode子目录")

        logging.info(f"找到 {len(episode_dirs)} 个episode目录")

        # 处理每个episode目录
        episode_idx = 0
        for episode_dir in episode_dirs:
            logging.info(f"处理episode {episode_idx}: {episode_dir}")

            # 获取该episode目录下的所有PKL文件
            pkl_files = sorted(list(episode_dir.glob("*.pkl")))
            if not pkl_files:
                logging.warning(f"Episode目录 {episode_dir} 中没有PKL文件，跳过")
                continue

            logging.info(f"Episode {episode_idx} 包含 {len(pkl_files)} 个PKL文件")

            # 使用简单的FPS基础时间戳，不需要复杂的时间验证
            logging.info(f"使用FPS={DEFAULT_FPS}的递增时间戳，每帧间隔{1/DEFAULT_FPS:.3f}秒")

            # 使用多进程处理该episode的所有帧
            frames_added = 0

            # 分批处理以避免内存问题
            batch_size = DEFAULT_NUM_WORKERS * 2  # 每批处理的文件数

            for i in range(0, len(pkl_files), batch_size):
                batch_files = pkl_files[i:i + batch_size]
                logging.info(f"Episode {episode_idx}: 处理批次 {i//batch_size + 1}/{(len(pkl_files) + batch_size - 1)//batch_size}, 包含 {len(batch_files)} 个文件")

                # 使用多进程处理当前批次，但保持时间顺序
                if DEFAULT_USE_PROCESSES:
                    with ProcessPoolExecutor(max_workers=DEFAULT_NUM_WORKERS) as executor:
                        # 提交所有任务，保持文件顺序，传递正确的frame_index和分辨率参数
                        futures = [executor.submit(process_pkl_file, pkl_file, task, frames_added + j, use_original_resolution)
                                 for j, pkl_file in enumerate(batch_files)]

                        # 收集所有结果，保持与文件的对应关系
                        batch_results = []
                        for j, future in enumerate(futures):
                            pkl_file = batch_files[j]
                            try:
                                frame_data = future.result()
                                if frame_data is not None:
                                    # 使用简单的递增时间戳，确保顺序
                                    simple_timestamp = (frames_added + j) / DEFAULT_FPS
                                    batch_results.append((simple_timestamp, pkl_file, frame_data))
                                else:
                                    logging.warning(f"跳过无效的PKL文件: {pkl_file}")
                            except Exception as e:
                                logging.error(f"处理PKL文件时出错 {pkl_file}: {e}")
                                continue

                        # 按时间戳排序，确保绝对的时间顺序
                        batch_results.sort(key=lambda x: x[0] if x[0] is not None else 0)

                        # 按排序后的顺序添加到数据集
                        for timestamp, pkl_file, frame_data in batch_results:
                            try:
                                # 只在第一帧打印详细信息
                                if frames_added == 0:
                                    logging.info(f"Frame data keys: {list(frame_data.keys())}")
                                    for key, value in frame_data.items():
                                        if isinstance(value, np.ndarray):
                                            logging.info(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                                        else:
                                            logging.info(f"  {key}: type={type(value)}, value={value}")

                                # 添加帧到数据集，需要单独传递task参数
                                task_name = frame_data.pop('task')  # 从frame_data中移除task
                                dataset.add_frame(frame_data, task=task_name)
                                frames_added += 1

                                if frames_added % 20 == 0:
                                    logging.info(f"Episode {episode_idx}: 已添加 {frames_added} 帧")

                            except Exception as e:
                                logging.error(f"添加帧时出错 (episode {episode_idx}, file {pkl_file}): {e}")
                                continue
                else:
                    # 单线程处理（用于调试）- 也确保时间顺序
                    batch_results = []
                    for j, pkl_file in enumerate(batch_files):
                        frame_data = process_pkl_file(pkl_file, task, frames_added + j, use_original_resolution)
                        if frame_data is not None:
                            # 使用简单的递增时间戳
                            simple_timestamp = (frames_added + j) / DEFAULT_FPS
                            batch_results.append((simple_timestamp, pkl_file, frame_data))
                        else:
                            logging.warning(f"跳过无效的PKL文件: {pkl_file}")

                    # 按时间戳排序
                    batch_results.sort(key=lambda x: x[0] if x[0] is not None else 0)

                    # 按排序后的顺序添加到数据集
                    for timestamp, pkl_file, frame_data in batch_results:
                        try:
                            if frames_added == 0:
                                logging.info(f"Frame data keys: {list(frame_data.keys())}")
                                for key, value in frame_data.items():
                                    if isinstance(value, np.ndarray):
                                        logging.info(f"  {key}: shape={value.shape}, dtype={value.dtype}")
                                    else:
                                        logging.info(f"  {key}: type={type(value)}, value={value}")

                            # 添加帧到数据集，需要单独传递task参数
                            task_name = frame_data.pop('task')  # 从frame_data中移除task
                            dataset.add_frame(frame_data, task=task_name)
                            frames_added += 1

                            if frames_added % 20 == 0:
                                logging.info(f"Episode {episode_idx}: 已添加 {frames_added} 帧")

                        except Exception as e:
                            logging.error(f"添加帧时出错 (episode {episode_idx}, file {pkl_file}): {e}")
                            continue

            if frames_added > 0:
                # 保存当前episode
                dataset.save_episode()
                logging.info(f"✅ Episode {episode_idx} 完成，共添加 {frames_added} 帧")
                episode_idx += 1
            else:
                logging.warning(f"Episode {episode_idx} 没有成功添加任何帧")

        # 最终保存
        logging.info("保存数据集...")
        logging.info(f"成功保存 {episode_idx} 个episodes")

        # 移动到最终目录
        if final_output_path.exists():
            shutil.rmtree(final_output_path)
        shutil.move(str(temp_output_path), str(final_output_path))

        logging.info(f"✅ 训练格式数据集已保存到: {final_output_path}")
        logging.info(f"总共处理了 {episode_idx} 个episodes")

def main():
    """主函数"""
    global DEFAULT_NUM_WORKERS, DEFAULT_USE_PROCESSES

    parser = argparse.ArgumentParser(description="将PKL文件转换为训练格式的LeRobot数据集")
    parser.add_argument("input_dir", help="包含PKL文件的输入目录")
    parser.add_argument("task", help="任务名称（如：manipulation, pick_and_place, assembly等）")
    parser.add_argument("--output_dir", help="输出目录（默认为输入目录下的lerobot目录）")
    parser.add_argument("--repo_id", default="training_dataset", help="数据集ID（默认：training_dataset）")
    parser.add_argument("--robot_type", default=DEFAULT_ROBOT_TYPE, help=f"机器人类型（默认：{DEFAULT_ROBOT_TYPE}）")
    parser.add_argument("--overwrite", action="store_true", help="覆盖现有输出目录")
    parser.add_argument("--num_workers", type=int, default=DEFAULT_NUM_WORKERS, help=f"并行处理的进程数（默认：{DEFAULT_NUM_WORKERS}）")
    parser.add_argument("--no_multiprocessing", "--no_multi",action="store_true", help="禁用多进程处理（用于调试）")
    parser.add_argument("--original_resolution", action="store_true", help="使用原始图像分辨率，不进行resize到224x224")

    args = parser.parse_args()

    # 设置默认输出目录
    if not args.output_dir:
        args.output_dir = os.path.join(args.input_dir, "lerobot")

    # 设置全局变量
    DEFAULT_NUM_WORKERS = args.num_workers
    DEFAULT_USE_PROCESSES = not args.no_multiprocessing

    # 设置日志记录
    setup_logging(args.input_dir)

    logging.info(f"多进程设置: {'启用' if DEFAULT_USE_PROCESSES else '禁用'}, 进程数: {DEFAULT_NUM_WORKERS}")

    try:
        # 创建训练格式数据集
        create_training_dataset(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            repo_id=args.repo_id,
            robot_type=args.robot_type,
            task=args.task,
            overwrite=args.overwrite,
            use_original_resolution=args.original_resolution
        )

        logging.info("🎉 转换完成！")

    except Exception as e:
        logging.error(f"转换失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
