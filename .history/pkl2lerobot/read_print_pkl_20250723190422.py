#!/usr/bin/env python3
"""
Script to read and analyze pkl file contents in detail
Focus on image data structure and timestamps
"""

import pickle
import os
import sys
import numpy as np
from datetime import datetime
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.cm as cm

def analyze_data_structure(data, prefix="", max_depth=3, current_depth=0):
    """
    Recursively analyze data structure
    """
    if current_depth > max_depth:
        return
    
    if isinstance(data, dict):
        print(f"{prefix}Dict with {len(data)} keys:")
        for key, value in data.items():
            print(f"{prefix}  '{key}': {type(value)}")
            if isinstance(value, np.ndarray):
                print(f"{prefix}    Shape: {value.shape}, dtype: {value.dtype}")
            elif isinstance(value, (list, tuple)) and len(value) > 0:
                print(f"{prefix}    Length: {len(value)}, first item type: {type(value[0])}")
            elif isinstance(value, (int, float, str)):
                print(f"{prefix}    Value: {value}")
            
            # Recurse for nested structures
            if isinstance(value, (dict, list)) and current_depth < max_depth:
                analyze_data_structure(value, prefix + "    ", max_depth, current_depth + 1)
    
    elif isinstance(data, (list, tuple)):
        print(f"{prefix}List/Tuple with {len(data)} items:")
        if len(data) > 0:
            print(f"{prefix}  First item type: {type(data[0])}")
            if len(data) <= 5:  # Show details for small lists
                for i, item in enumerate(data):
                    print(f"{prefix}  [{i}]: {type(item)}")
                    if isinstance(item, np.ndarray):
                        print(f"{prefix}      Shape: {item.shape}, dtype: {item.dtype}")
            
            # Recurse for first item if it's a complex structure
            if isinstance(data[0], (dict, list)) and current_depth < max_depth:
                print(f"{prefix}  Analyzing first item:")
                analyze_data_structure(data[0], prefix + "    ", max_depth, current_depth + 1)
    
    elif isinstance(data, np.ndarray):
        print(f"{prefix}NumPy array: shape={data.shape}, dtype={data.dtype}")
        if data.size < 20:  # Show small arrays
            print(f"{prefix}  Values: {data}")


def check_image_data(data, path_info=""):
    """
    Specifically check for image data patterns
    """
    print(f"\n🔍 Image Data Analysis {path_info}")
    print("=" * 50)

    image_info = {}

    def find_images(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key

                # Check if this looks like image data
                if 'image' in key.lower() or 'camera' in key.lower() or 'rgb' in key.lower():
                    print(f"📷 Found potential image key: {current_path}")
                    if isinstance(value, np.ndarray):
                        print(f"   Shape: {value.shape}, dtype: {value.dtype}")
                        if len(value.shape) == 3 and value.shape[2] in [3, 4]:
                            print(f"   ✅ Looks like RGB/RGBA image: {value.shape}")
                            image_info[current_path] = {
                                'shape': value.shape,
                                'dtype': value.dtype,
                                'size_mb': value.nbytes / (1024*1024)
                            }
                        elif len(value.shape) == 1 or (len(value.shape) == 2 and value.shape[1] == 1):
                            # Handle both 1D arrays and 2D arrays with shape (N, 1)
                            if len(value.shape) == 2 and value.shape[1] == 1:
                                # Flatten (N, 1) to (N,) for decoding
                                decode_array = value.flatten()
                                print(f"   🤔 Might be compressed image data: {value.shape} -> flattened to {decode_array.shape}")
                            else:
                                decode_array = value
                                print(f"   🤔 Might be compressed image data: {value.shape}")

                            # Try to decode as JPEG/PNG
                            try:
                                decoded = cv2.imdecode(decode_array, cv2.IMREAD_COLOR)
                                if decoded is not None:
                                    print(f"   ✅ Successfully decoded to: {decoded.shape}")
                                    image_info[current_path] = {
                                        'shape': value.shape,
                                        'decoded_shape': decoded.shape,
                                        'dtype': value.dtype,
                                        'size_mb': value.nbytes / (1024*1024),
                                        'note': 'compressed image (JPEG/PNG)'
                                    }
                                else:
                                    print(f"   ❌ Could not decode as standard image format")
                                    image_info[current_path] = {
                                        'shape': value.shape,
                                        'dtype': value.dtype,
                                        'size_mb': value.nbytes / (1024*1024),
                                        'note': 'failed to decode'
                                    }
                            except Exception as e:
                                print(f"   ❌ Could not decode as image: {e}")
                                image_info[current_path] = {
                                    'shape': value.shape,
                                    'dtype': value.dtype,
                                    'size_mb': value.nbytes / (1024*1024),
                                    'note': 'decode error'
                                }
                    elif isinstance(value, dict):
                        print(f"   📁 Image dict with keys: {list(value.keys())}")
                        # Analyze each camera in the images dict
                        for cam_key, cam_value in value.items():
                            if isinstance(cam_value, np.ndarray):
                                print(f"     📷 Camera '{cam_key}': shape={cam_value.shape}, dtype={cam_value.dtype}")
                                if len(cam_value.shape) == 1 or (len(cam_value.shape) == 2 and cam_value.shape[1] == 1):
                                    # Handle both 1D arrays and 2D arrays with shape (N, 1)
                                    if len(cam_value.shape) == 2 and cam_value.shape[1] == 1:
                                        decode_array = cam_value.flatten()
                                        print(f"       🤔 2D array with shape (N,1) - likely compressed image, flattening to {decode_array.shape}")
                                    else:
                                        decode_array = cam_value
                                        print(f"       🤔 1D array - likely compressed image")

                                    # Try to decode
                                    try:
                                        decoded = cv2.imdecode(decode_array, cv2.IMREAD_COLOR)
                                        if decoded is not None:
                                            print(f"       ✅ Decoded to: {decoded.shape}")
                                            image_info[f"{current_path}.{cam_key}"] = {
                                                'shape': cam_value.shape,
                                                'decoded_shape': decoded.shape,
                                                'dtype': cam_value.dtype,
                                                'size_mb': cam_value.nbytes / (1024*1024),
                                                'note': 'compressed image'
                                            }
                                        else:
                                            print(f"       ❌ Could not decode as standard image format")
                                            image_info[f"{current_path}.{cam_key}"] = {
                                                'shape': cam_value.shape,
                                                'dtype': cam_value.dtype,
                                                'size_mb': cam_value.nbytes / (1024*1024),
                                                'note': 'failed to decode'
                                            }
                                    except Exception as e:
                                        print(f"       ❌ Decode error: {e}")
                                        image_info[f"{current_path}.{cam_key}"] = {
                                            'shape': cam_value.shape,
                                            'dtype': cam_value.dtype,
                                            'size_mb': cam_value.nbytes / (1024*1024),
                                            'note': 'decode error'
                                        }
                                elif len(cam_value.shape) == 3:
                                    print(f"       ✅ 3D array - likely raw RGB image")
                                    image_info[f"{current_path}.{cam_key}"] = {
                                        'shape': cam_value.shape,
                                        'dtype': cam_value.dtype,
                                        'size_mb': cam_value.nbytes / (1024*1024),
                                        'note': 'raw RGB image'
                                    }
                        find_images(value, current_path)

                # Recurse into nested structures
                elif isinstance(value, dict):
                    find_images(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_images(value[0], f"{current_path}[0]")

    find_images(data)
    return image_info


def check_timestamp_data(data, path_info=""):
    """
    Check for timestamp information
    """
    print(f"\n⏰ Timestamp Analysis {path_info}")
    print("=" * 50)
    
    timestamp_info = {}
    
    def find_timestamps(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key
                
                # Check if this looks like timestamp data
                if any(word in key.lower() for word in ['time', 'stamp', 'date']):
                    print(f"⏰ Found potential timestamp key: {current_path}")
                    print(f"   Type: {type(value)}, Value: {value}")
                    timestamp_info[current_path] = {
                        'type': str(type(value)),
                        'value': value
                    }
                
                # Recurse into nested structures
                if isinstance(value, dict):
                    find_timestamps(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_timestamps(value[0], f"{current_path}[0]")
    
    find_timestamps(data)
    return timestamp_info


def check_arm_joints_data(data, path_info=""):
    """
    Check for arm_joints information
    """
    print(f"\n🦾 Arm Joints Analysis {path_info}")
    print("=" * 50)

    arm_joints_info = {}

    def find_arm_joints(obj, key_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key

                # Check if this looks like arm_joints data
                if 'arm' in key.lower() or 'joint' in key.lower() or key.lower() == 'arm_joints':
                    print(f"🦾 Found potential arm joints key: {current_path}")
                    print(f"   Type: {type(value)}")

                    if isinstance(value, np.ndarray):
                        print(f"   Shape: {value.shape}, dtype: {value.dtype}")
                        print(f"   Values: {value}")
                        arm_joints_info[current_path] = {
                            'type': str(type(value)),
                            'shape': value.shape,
                            'dtype': value.dtype,
                            'values': value.tolist() if value.size < 20 else "Array too large to display"
                        }
                    else:
                        print(f"   Value: {value}")
                        arm_joints_info[current_path] = {
                            'type': str(type(value)),
                            'value': value
                        }

                # Recurse into nested structures
                if isinstance(value, dict):
                    find_arm_joints(value, current_path)
                elif isinstance(value, (list, tuple)) and len(value) > 0 and isinstance(value[0], dict):
                    find_arm_joints(value[0], f"{current_path}[0]")

    find_arm_joints(data)
    return arm_joints_info


def save_images_from_pkl(data, output_dir, pkl_filename="", max_images_per_camera=None):
    """
    Extract and save images from pkl data

    Args:
        data: The loaded pkl data
        output_dir: Directory to save images
        pkl_filename: Name of the pkl file (for naming output files)
        max_images_per_camera: Maximum number of images to save per camera (None for all)

    Returns:
        dict: Information about saved images
    """
    print(f"\n💾 Saving Images from PKL Data")
    print("=" * 50)

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    saved_images = {}
    image_counter = 0

    def save_image_data(image_array, camera_name, frame_index=None, is_compressed=False):
        """Helper function to save image data"""
        nonlocal image_counter

        try:
            # Check if this is depth data
            is_depth = 'depth' in camera_name.lower()

            if is_compressed:
                # Handle both 1D and 2D (N,1) compressed image data
                if len(image_array.shape) == 2 and image_array.shape[1] == 1:
                    decode_array = image_array.flatten()
                else:
                    decode_array = image_array

                # Decode compressed image data
                if is_depth:
                    # For depth data, try to decode as grayscale first
                    decoded = cv2.imdecode(decode_array, cv2.IMREAD_UNCHANGED)
                    if decoded is None:
                        decoded = cv2.imdecode(decode_array, cv2.IMREAD_GRAYSCALE)
                else:
                    decoded = cv2.imdecode(decode_array, cv2.IMREAD_COLOR)

                if decoded is None:
                    print(f"   ❌ Failed to decode compressed image for {camera_name}")
                    return False
                image_to_save = decoded
            else:
                # Handle raw RGB image data
                image_to_save = image_array

            # Special processing for depth images
            if is_depth:
                # Convert to grayscale if it's a 3-channel depth image
                if len(image_to_save.shape) == 3:
                    if image_to_save.shape[2] == 3:
                        # Convert BGR/RGB to grayscale
                        image_to_save = cv2.cvtColor(image_to_save, cv2.COLOR_BGR2GRAY)
                    elif image_to_save.shape[2] == 1:
                        # Remove single channel dimension
                        image_to_save = image_to_save.squeeze(axis=2)

                # Apply jet colormap to depth data
                # Normalize depth values to 0-1 range
                if image_to_save.dtype != np.uint8:
                    # Handle different depth data types
                    depth_min = image_to_save.min()
                    depth_max = image_to_save.max()
                    if depth_max > depth_min:
                        normalized_depth = (image_to_save - depth_min) / (depth_max - depth_min)
                    else:
                        normalized_depth = np.zeros_like(image_to_save)
                else:
                    # For uint8 depth data
                    normalized_depth = image_to_save.astype(np.float32) / 255.0

                # Apply jet colormap
                import matplotlib.cm as cm
                colored_depth = cm.jet(normalized_depth)  # Returns RGBA
                # Convert to RGB and scale to 0-255
                image_to_save = (colored_depth[:, :, :3] * 255).astype(np.uint8)

                print(f"   🌈 Applied jet colormap to depth image: {image_to_save.shape}")
            else:
                # Convert BGR to RGB if needed (OpenCV uses BGR by default)
                if len(image_to_save.shape) == 3 and image_to_save.shape[2] == 3:
                    if is_compressed:  # cv2.imdecode returns BGR
                        image_to_save = cv2.cvtColor(image_to_save, cv2.COLOR_BGR2RGB)

            # Generate filename
            base_name = pkl_filename.replace('.pkl', '') if pkl_filename else 'image'
            if frame_index is not None:
                filename = f"{base_name}_{camera_name}_frame_{frame_index:04d}.jpg"
            else:
                filename = f"{base_name}_{camera_name}_{image_counter:04d}.jpg"

            filepath = os.path.join(output_dir, filename)

            # Save using PIL for better format support
            if image_to_save.dtype != np.uint8:
                # Normalize to 0-255 range if needed
                if image_to_save.max() <= 1.0:
                    image_to_save = (image_to_save * 255).astype(np.uint8)
                else:
                    image_to_save = image_to_save.astype(np.uint8)

            pil_image = Image.fromarray(image_to_save)
            pil_image.save(filepath, 'JPEG', quality=95)

            depth_info = " (depth with jet colormap)" if is_depth else ""
            print(f"   ✅ Saved: {filename} ({image_to_save.shape}){depth_info}")

            if camera_name not in saved_images:
                saved_images[camera_name] = []
            saved_images[camera_name].append({
                'filename': filename,
                'filepath': filepath,
                'shape': image_to_save.shape,
                'frame_index': frame_index,
                'is_depth': is_depth
            })

            image_counter += 1
            return True

        except Exception as e:
            print(f"   ❌ Error saving image for {camera_name}: {e}")
            import traceback
            traceback.print_exc()
            return False

    def extract_images(obj, key_path="", frame_index=None):
        """Recursively extract images from data structure"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{key_path}.{key}" if key_path else key

                # Check if this looks like image data
                if 'image' in key.lower() or 'camera' in key.lower() or 'rgb' in key.lower():
                    if isinstance(value, np.ndarray):
                        # Single image array
                        camera_name = key.replace('/', '_').replace('\\', '_')  # Clean camera name
                        is_compressed = len(value.shape) == 1 or (len(value.shape) == 2 and value.shape[1] == 1)
                        save_image_data(value, camera_name, frame_index, is_compressed)

                    elif isinstance(value, dict):
                        # Dictionary of camera images
                        for cam_key, cam_value in value.items():
                            if isinstance(cam_value, np.ndarray):
                                camera_name = f"{key}_{cam_key}".replace('/', '_').replace('\\', '_')
                                is_compressed = len(cam_value.shape) == 1 or (len(cam_value.shape) == 2 and cam_value.shape[1] == 1)

                                # Check max images limit
                                if max_images_per_camera is not None:
                                    if camera_name in saved_images and len(saved_images[camera_name]) >= max_images_per_camera:
                                        continue

                                save_image_data(cam_value, camera_name, frame_index, is_compressed)

                # Recurse into nested structures
                elif isinstance(value, dict):
                    extract_images(value, current_path, frame_index)
                elif isinstance(value, (list, tuple)) and len(value) > 0:
                    # Handle list of frames/timesteps
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            # Check max images limit for lists
                            if max_images_per_camera is not None and i >= max_images_per_camera:
                                break
                            extract_images(item, f"{current_path}[{i}]", i)

    # Start extraction
    extract_images(data)

    # Print summary
    total_saved = sum(len(images) for images in saved_images.values())
    print(f"\n📊 Image Extraction Summary:")
    print(f"   Total cameras found: {len(saved_images)}")
    print(f"   Total images saved: {total_saved}")
    print(f"   Output directory: {output_dir}")

    for camera_name, images in saved_images.items():
        print(f"   📷 {camera_name}: {len(images)} images")

    return saved_images


def analyze_pkl_file(pkl_path, save_images=False, output_dir='./extracted_images', max_images=None):
    """
    Comprehensive analysis of a pkl file

    Args:
        pkl_path: Path to the pkl file
        save_images: Whether to save extracted images
        output_dir: Directory to save images
        max_images: Maximum number of images to save per camera
    """
    print(f"📄 Analyzing PKL file: {os.path.basename(pkl_path)}")
    print("=" * 80)

    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)

        print(f"✅ Successfully loaded pkl file")
        print(f"📊 Root data type: {type(data)}")

        if isinstance(data, dict):
            print(f"📋 Root keys: {list(data.keys())}")
        elif isinstance(data, (list, tuple)):
            print(f"📋 Root list length: {len(data)}")

        print(f"💾 File size: {os.path.getsize(pkl_path) / (1024*1024):.2f} MB")

        # Detailed structure analysis
        print(f"\n🏗️ Data Structure Analysis")
        print("=" * 50)
        analyze_data_structure(data)

        # Image data analysis
        image_info = check_image_data(data, f"({os.path.basename(pkl_path)})")

        # Timestamp analysis
        timestamp_info = check_timestamp_data(data, f"({os.path.basename(pkl_path)})")

        # Arm joints analysis
        arm_joints_info = check_arm_joints_data(data, f"({os.path.basename(pkl_path)})")

        # Save images if requested
        saved_images_info = {}
        if save_images and image_info:
            pkl_filename = os.path.basename(pkl_path)
            saved_images_info = save_images_from_pkl(data, output_dir, pkl_filename, max_images)

        # Summary
        print(f"\n📋 Summary for {os.path.basename(pkl_path)}")
        print("=" * 50)
        print(f"🖼️  Found {len(image_info)} potential image data entries")
        for path, info in image_info.items():
            print(f"   {path}: {info['shape']} ({info['size_mb']:.2f} MB)")

        if saved_images_info:
            total_saved = sum(len(images) for images in saved_images_info.values())
            print(f"💾 Saved {total_saved} images to {output_dir}")

        print(f"⏰ Found {len(timestamp_info)} potential timestamp entries")
        for path, info in timestamp_info.items():
            print(f"   {path}: {info['value']}")

        print(f"🦾 Found {len(arm_joints_info)} potential arm joints entries")
        for path, info in arm_joints_info.items():
            print(f"   {path}: {info['values'] if 'values' in info else info['value']}")

        return {
            'images': image_info,
            'timestamps': timestamp_info,
            'arm_joints': arm_joints_info,
            'saved_images': saved_images_info,
            'root_type': type(data),
            'file_size_mb': os.path.getsize(pkl_path) / (1024*1024)
        }

    except Exception as e:
        print(f"❌ Error loading pkl file: {e}")
        return None


def main():
    if len(sys.argv) < 2:
        print("Usage: python read_print_pkl.py <pkl_file_or_directory> [--save-images] [--output-dir <dir>] [--max-images <num>]")
        print("Examples:")
        print("  python read_print_pkl.py /path/to/file.pkl")
        print("  python read_print_pkl.py /path/to/file.pkl --save-images")
        print("  python read_print_pkl.py /path/to/file.pkl --save-images --output-dir ./images")
        print("  python read_print_pkl.py /path/to/file.pkl --save-images --max-images 10")
        print("  python read_print_pkl.py /path/to/pkl_directory --save-images")
        return

    input_path = sys.argv[1]
    save_images = '--save-images' in sys.argv

    # Parse output directory
    output_dir = './extracted_images'
    if '--output-dir' in sys.argv:
        try:
            output_dir_idx = sys.argv.index('--output-dir') + 1
            if output_dir_idx < len(sys.argv):
                output_dir = sys.argv[output_dir_idx]
        except (ValueError, IndexError):
            print("❌ Invalid --output-dir argument")
            return

    # Parse max images per camera
    max_images = None
    if '--max-images' in sys.argv:
        try:
            max_images_idx = sys.argv.index('--max-images') + 1
            if max_images_idx < len(sys.argv):
                max_images = int(sys.argv[max_images_idx])
        except (ValueError, IndexError):
            print("❌ Invalid --max-images argument")
            return
    
    if os.path.isfile(input_path) and input_path.endswith('.pkl'):
        # Single file analysis
        analyze_pkl_file(input_path, save_images, output_dir, max_images)
    
    elif os.path.isdir(input_path):
        # Directory analysis - analyze first few files
        pkl_files = [f for f in os.listdir(input_path) if f.endswith('.pkl')]
        pkl_files.sort()
        
        if not pkl_files:
            print(f"❌ No pkl files found in {input_path}")
            return
        
        print(f"📁 Found {len(pkl_files)} pkl files in directory")
        print(f"🔍 Analyzing first 3 files for structure comparison...")
        
        all_results = {}
        for i, pkl_file in enumerate(pkl_files[:6]):
            pkl_path = os.path.join(input_path, pkl_file)
            print(f"\n{'='*80}")
            print(f"FILE {i+1}/{min(6, len(pkl_files))}")
            result = analyze_pkl_file(pkl_path, save_images, output_dir, max_images)
            if result:
                all_results[pkl_file] = result
        
        # Compare structures across files
        print(f"\n🔄 Cross-file Comparison")
        print("=" * 80)
        
        if all_results:
            # Compare image structures
            print("🖼️  Image Data Consistency:")

            for file_name, result in all_results.items():
                images = result['images']
                print(f"   {file_name}: {len(images)} image entries")
                for path, info in images.items():
                    print(f"     {path}: {info['shape']}")
            
            # Compare timestamp structures
            print("\n⏰ Timestamp Data Consistency:")
            for file_name, result in all_results.items():
                timestamps = result['timestamps']
                print(f"   {file_name}: {len(timestamps)} timestamp entries")
                for path, info in timestamps.items():
                    print(f"     {path}: {info['value']}")
    
    else:
        print(f"❌ Invalid path: {input_path}")


if __name__ == "__main__":
    main()
