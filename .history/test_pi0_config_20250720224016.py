#!/usr/bin/env python3
"""
测试 PI0 模型配置是否正确
"""

import sys
import os
sys.path.append('src')

from lerobot.configs.policies import PreTrainedConfig

def test_pi0_config():
    """测试 PI0 配置是否能正确加载"""
    try:
        print("正在测试 PI0 配置加载...")
        
        # 测试从本地路径加载配置
        config_path = "models/pi0_base"
        print(f"尝试从路径加载配置: {config_path}")
        
        # 检查配置文件是否存在
        config_file = os.path.join(config_path, "config.json")
        if not os.path.exists(config_file):
            print(f"错误: 配置文件不存在: {config_file}")
            return False
            
        print(f"配置文件存在: {config_file}")
        
        # 尝试加载配置
        config = PreTrainedConfig.from_pretrained(config_path)
        print(f"成功加载配置!")
        print(f"模型类型: {config.type}")
        print(f"冻结视觉编码器: {config.freeze_vision_encoder}")
        print(f"只训练专家网络: {config.train_expert_only}")
        print(f"训练状态投影: {config.train_state_proj}")
        
        return True
        
    except Exception as e:
        print(f"配置加载失败: {e}")
        return False

if __name__ == "__main__":
    success = test_pi0_config()
    if success:
        print("\n✅ PI0 配置测试通过!")
        print("可以使用修复后的训练脚本进行训练。")
    else:
        print("\n❌ PI0 配置测试失败!")
        print("请检查模型文件是否完整。")
    
    sys.exit(0 if success else 1)
