#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import collections
from dataclasses import dataclass, field
from typing import Any, Callable, Sequence

import torch
from torchvision.transforms import v2
from torchvision.transforms.v2 import Transform
from torchvision.transforms.v2 import functional as F  # noqa: N812


class RandomSubsetApply(Transform):
    """Apply a random subset of N transformations from a list of transformations.

    Args:
        transforms: list of transformations.
        p: represents the multinomial probabilities (with no replacement) used for sampling the transform.
            If the sum of the weights is not 1, they will be normalized. If ``None`` (default), all transforms
            have the same probability.
        n_subset: number of transformations to apply. If ``None``, all transforms are applied.
            Must be in [1, len(transforms)].
        random_order: apply transformations in a random order.
    """

    def __init__(
        self,
        transforms: Sequence[Callable],
        p: list[float] | None = None,
        n_subset: int | None = None,
        random_order: bool = False,
    ) -> None:
        super().__init__()
        if not isinstance(transforms, Sequence):
            raise TypeError("Argument transforms should be a sequence of callables")
        if p is None:
            p = [1] * len(transforms)
        elif len(p) != len(transforms):
            raise ValueError(
                f"Length of p doesn't match the number of transforms: {len(p)} != {len(transforms)}"
            )

        if n_subset is None:
            n_subset = len(transforms)
        elif not isinstance(n_subset, int):
            raise TypeError("n_subset should be an int or None")
        elif not (1 <= n_subset <= len(transforms)):
            raise ValueError(f"n_subset should be in the interval [1, {len(transforms)}]")

        self.transforms = transforms
        total = sum(p)
        self.p = [prob / total for prob in p]
        self.n_subset = n_subset
        self.random_order = random_order

        self.selected_transforms = None

    def forward(self, *inputs: Any) -> Any:
        needs_unpacking = len(inputs) > 1

        selected_indices = torch.multinomial(torch.tensor(self.p), self.n_subset)
        if not self.random_order:
            selected_indices = selected_indices.sort().values

        self.selected_transforms = [self.transforms[i] for i in selected_indices]

        for transform in self.selected_transforms:
            outputs = transform(*inputs)
            inputs = outputs if needs_unpacking else (outputs,)

        return outputs

    def extra_repr(self) -> str:
        return (
            f"transforms={self.transforms}, "
            f"p={self.p}, "
            f"n_subset={self.n_subset}, "
            f"random_order={self.random_order}"
        )


class SharpnessJitter(Transform):
    """Randomly change the sharpness of an image or video.

    Similar to a v2.RandomAdjustSharpness with p=1 and a sharpness_factor sampled randomly.
    While v2.RandomAdjustSharpness applies — with a given probability — a fixed sharpness_factor to an image,
    SharpnessJitter applies a random sharpness_factor each time. This is to have a more diverse set of
    augmentations as a result.

    A sharpness_factor of 0 gives a blurred image, 1 gives the original image while 2 increases the sharpness
    by a factor of 2.

    If the input is a :class:`torch.Tensor`,
    it is expected to have [..., 1 or 3, H, W] shape, where ... means an arbitrary number of leading dimensions.

    Args:
        sharpness: How much to jitter sharpness. sharpness_factor is chosen uniformly from
            [max(0, 1 - sharpness), 1 + sharpness] or the given
            [min, max]. Should be non negative numbers.
    """

    def __init__(self, sharpness: float | Sequence[float]) -> None:
        super().__init__()
        self.sharpness = self._check_input(sharpness)

    def _check_input(self, sharpness):
        if isinstance(sharpness, (int, float)):
            if sharpness < 0:
                raise ValueError("If sharpness is a single number, it must be non negative.")
            sharpness = [1.0 - sharpness, 1.0 + sharpness]
            sharpness[0] = max(sharpness[0], 0.0)
        elif isinstance(sharpness, collections.abc.Sequence) and len(sharpness) == 2:
            sharpness = [float(v) for v in sharpness]
        else:
            raise TypeError(f"{sharpness=} should be a single number or a sequence with length 2.")

        if not 0.0 <= sharpness[0] <= sharpness[1]:
            raise ValueError(f"sharpness values should be between (0., inf), but got {sharpness}.")

        return float(sharpness[0]), float(sharpness[1])

    def make_params(self, flat_inputs: list[Any]) -> dict[str, Any]:
        sharpness_factor = torch.empty(1).uniform_(self.sharpness[0], self.sharpness[1]).item()
        return {"sharpness_factor": sharpness_factor}

    def transform(self, inpt: Any, params: dict[str, Any]) -> Any:
        sharpness_factor = params["sharpness_factor"]
        return self._call_kernel(F.adjust_sharpness, inpt, sharpness_factor=sharpness_factor)


class SaveStepImages(Transform):
    """Save images at specific steps without any transformation.

    This transform saves original images at specified steps (0, 10, 20, 30, 40, 50)
    without applying any modifications. Useful for debugging and visualization.
    """

    def __init__(self, save_dir: str = "step_images", target_steps: list[int] = None) -> None:
        super().__init__()
        self.save_dir = save_dir
        self.target_steps = target_steps if target_steps is not None else [0, 10, 20, 30, 40, 50]
        self.step_counter = 0

        import os
        os.makedirs(self.save_dir, exist_ok=True)
        print(f"📸 SaveStepImages: 将在步骤 {self.target_steps} 保存图像到 {self.save_dir}/")

    def forward(self, *inputs: Any) -> Any:
        # Process each input but don't modify them
        outputs = []
        for inpt in inputs:
            if isinstance(inpt, torch.Tensor) and self.step_counter in self.target_steps:
                self._save_step_image(inpt)
            outputs.append(inpt)  # Return original input unchanged

        self.step_counter += 1
        return outputs[0] if len(outputs) == 1 else outputs

    def _save_step_image(self, img: torch.Tensor) -> None:
        """Save the original image at specific steps."""
        try:
            import matplotlib.pyplot as plt
            from torchvision.transforms import ToPILImage

            # 转换为PIL图像用于保存
            to_pil = ToPILImage()

            # 确保图像值在[0,1]范围内
            img_norm = torch.clamp(img, 0, 1)

            # 保存图像
            img_pil = to_pil(img_norm)
            save_path = f"{self.save_dir}/step_{self.step_counter:03d}.png"
            img_pil.save(save_path)

            print(f"📸 步骤 {self.step_counter}: 图像已保存到 {save_path}")

        except Exception as e:
            print(f"⚠️  保存步骤图像时出错: {e}")


class CropBottomHalf(Transform):
    """Crop the bottom half of the image.

    This transform crops the image to keep only the bottom half, effectively removing the top half.
    Useful for removing sky or ceiling from robot vision tasks.
    """

    def __init__(self, debug_save_images: bool = False, debug_save_dir: str = "debug_crop_images") -> None:
        super().__init__()
        self.debug_save_images = debug_save_images
        self.debug_save_dir = debug_save_dir
        self.debug_counter = 0

        if self.debug_save_images:
            import os
            os.makedirs(self.debug_save_dir, exist_ok=True)
            print(f"🔍 CropBottomHalf Debug: 将保存图像到 {self.debug_save_dir}/")

    def forward(self, *inputs: Any) -> Any:
        # Process each input
        outputs = []
        for inpt in inputs:
            if isinstance(inpt, torch.Tensor):
                outputs.append(self._crop_bottom_half(inpt))
            else:
                outputs.append(inpt)

        return outputs[0] if len(outputs) == 1 else outputs

    def _crop_bottom_half(self, img: torch.Tensor) -> torch.Tensor:
        """Crop the bottom half of the image."""
        if img.dim() == 3:  # CHW format
            _, height, width = img.shape
            # Crop from the middle to the bottom (keep bottom half)
            top = height // 2
            cropped_img = F.crop(img, top, 0, height - top, width)

            # Debug: 打印信息和保存图像（只在前几次执行时）
            if self.debug_save_images and self.debug_counter < 10:  # 只保存前10张图像
                self._debug_save_comparison(img, cropped_img)

            # 只在前几次执行时打印信息，避免日志过多
            if self.debug_counter < 5:
                print(f"🖼️  图像裁剪: {width}x{height} -> {cropped_img.shape[2]}x{cropped_img.shape[1]} (去掉上半部分)")
            return cropped_img

        elif img.dim() == 4:  # BCHW format
            _, _, height, width = img.shape
            top = height // 2
            cropped_img = F.crop(img, top, 0, height - top, width)

            # Debug: 只在前几次执行时打印信息
            if self.debug_counter < 5:
                print(f"🖼️  批量图像裁剪: {width}x{height} -> {cropped_img.shape[3]}x{cropped_img.shape[2]} (去掉上半部分)")
            return cropped_img
        else:
            raise ValueError(f"Expected 3D or 4D tensor, got {img.dim()}D tensor")

    def _debug_save_comparison(self, original_img: torch.Tensor, cropped_img: torch.Tensor) -> None:
        """保存原始图像和裁剪后图像的对比"""
        try:
            import matplotlib.pyplot as plt
            from torchvision.transforms import ToPILImage

            # 转换为PIL图像用于保存
            to_pil = ToPILImage()

            # 确保图像值在[0,1]范围内
            original_img_norm = torch.clamp(original_img, 0, 1)
            cropped_img_norm = torch.clamp(cropped_img, 0, 1)

            # 创建对比图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

            # 原始图像
            original_pil = to_pil(original_img_norm)
            ax1.imshow(original_pil)
            ax1.set_title(f"原始图像\n{original_img.shape[2]}x{original_img.shape[1]}")
            ax1.axis('off')

            # 裁剪后图像
            cropped_pil = to_pil(cropped_img_norm)
            ax2.imshow(cropped_pil)
            ax2.set_title(f"裁剪后图像\n{cropped_img.shape[2]}x{cropped_img.shape[1]}")
            ax2.axis('off')

            plt.tight_layout()

            # 保存对比图
            save_path = f"{self.debug_save_dir}/crop_comparison_{self.debug_counter:03d}.png"
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()

            print(f"📸 Debug图像已保存: {save_path}")
            self.debug_counter += 1

        except Exception as e:
            print(f"⚠️  保存debug图像时出错: {e}")


@dataclass
class ImageTransformConfig:
    """
    For each transform, the following parameters are available:
      weight: This represents the multinomial probability (with no replacement)
            used for sampling the transform. If the sum of the weights is not 1,
            they will be normalized.
      type: The name of the class used. This is either a class available under torchvision.transforms.v2 or a
            custom transform defined here.
      kwargs: Lower & upper bound respectively used for sampling the transform's parameter
            (following uniform distribution) when it's applied.
    """

    weight: float = 1.0
    type: str = "Identity"
    kwargs: dict[str, Any] = field(default_factory=dict)


@dataclass
class ImageTransformsConfig:
    """
    These transforms are all using standard torchvision.transforms.v2
    You can find out how these transformations affect images here:
    https://pytorch.org/vision/0.18/auto_examples/transforms/plot_transforms_illustrations.html
    We use a custom RandomSubsetApply container to sample them.
    """

    # Set this flag to `true` to enable transforms during training
    enable: bool = False
    # This is the maximum number of transforms (sampled from these below) that will be applied to each frame.
    # It's an integer in the interval [1, number_of_available_transforms].
    max_num_transforms: int = 3
    # By default, transforms are applied in Torchvision's suggested order (shown below).
    # Set this to True to apply them in a random order.
    random_order: bool = False
    tfs: dict[str, ImageTransformConfig] = field(
        default_factory=lambda: {
            "brightness": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"brightness": (0.8, 1.2)},
            ),
            "contrast": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"contrast": (0.8, 1.2)},
            ),
            "saturation": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"saturation": (0.5, 1.5)},
            ),
            "hue": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"hue": (-0.05, 0.05)},
            ),
            "sharpness": ImageTransformConfig(
                weight=1.0,
                type="SharpnessJitter",
                kwargs={"sharpness": (0.5, 1.5)},
            ),
                                    "crop_bottom_half": ImageTransformConfig(
                weight=0.0,  # 禁用裁剪变换
                type="CropBottomHalf",
                kwargs={},
            ),
        }
    )


def make_transform_from_config(cfg: ImageTransformConfig):
    if cfg.type == "Identity":
        return v2.Identity(**cfg.kwargs)
    elif cfg.type == "ColorJitter":
        return v2.ColorJitter(**cfg.kwargs)
    elif cfg.type == "SharpnessJitter":
        return SharpnessJitter(**cfg.kwargs)
    elif cfg.type == "CenterCrop":
        return v2.CenterCrop(**cfg.kwargs)
    elif cfg.type == "RandomCrop":
        return v2.RandomCrop(**cfg.kwargs)
    elif cfg.type == "Resize":
        return v2.Resize(**cfg.kwargs)
    elif cfg.type == "CropBottomHalf":
        return CropBottomHalf(**cfg.kwargs)
    elif cfg.type == "SaveStepImages":
        return SaveStepImages(**cfg.kwargs)
    else:
        raise ValueError(f"Transform '{cfg.type}' is not valid.")


class ImageTransforms(Transform):
    """A class to compose image transforms based on configuration."""

    def __init__(self, cfg: ImageTransformsConfig) -> None:
        super().__init__()
        self._cfg = cfg

        self.weights = []
        self.transforms = {}
        self.always_apply_transforms = []  # 始终应用的变换

        for tf_name, tf_cfg in cfg.tfs.items():
            if tf_cfg.weight <= 0.0:
                continue

            transform = make_transform_from_config(tf_cfg)

            # CropBottomHalf 变换应该始终应用，以确保批次中所有图像尺寸一致
            if tf_cfg.type == "CropBottomHalf":
                self.always_apply_transforms.append(transform)
            else:
                self.transforms[tf_name] = transform
                self.weights.append(tf_cfg.weight)

        # 构建变换序列
        transform_list = []

        # 首先应用始终应用的变换
        if self.always_apply_transforms:
            transform_list.extend(self.always_apply_transforms)

        # 然后应用随机变换
        n_subset = min(len(self.transforms), cfg.max_num_transforms)
        if n_subset > 0 and cfg.enable and len(self.transforms) > 0:
            random_transforms = RandomSubsetApply(
                transforms=list(self.transforms.values()),
                p=self.weights,
                n_subset=n_subset,
                random_order=cfg.random_order,
            )
            transform_list.append(random_transforms)

        # 组合所有变换
        if len(transform_list) == 0:
            self.tf = v2.Identity()
        elif len(transform_list) == 1:
            self.tf = transform_list[0]
        else:
            self.tf = v2.Compose(transform_list)

    def forward(self, *inputs: Any) -> Any:
        return self.tf(*inputs)
