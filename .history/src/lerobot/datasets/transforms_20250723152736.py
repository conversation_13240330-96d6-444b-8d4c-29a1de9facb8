#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import collections
import inspect
import os
from dataclasses import dataclass, field
from typing import Any, Callable, Sequence

import torch
from torchvision.transforms import v2
from torchvision.transforms.v2 import Transform
from torchvision.transforms.v2 import functional as F  # noqa: N812


class RandomSubsetApply(Transform):
    """Apply a random subset of N transformations from a list of transformations.

    Args:
        transforms: list of transformations.
        p: represents the multinomial probabilities (with no replacement) used for sampling the transform.
            If the sum of the weights is not 1, they will be normalized. If ``None`` (default), all transforms
            have the same probability.
        n_subset: number of transformations to apply. If ``None``, all transforms are applied.
            Must be in [1, len(transforms)].
        random_order: apply transformations in a random order.
    """

    def __init__(
        self,
        transforms: Sequence[Callable],
        p: list[float] | None = None,
        n_subset: int | None = None,
        random_order: bool = False,
    ) -> None:
        super().__init__()
        if not isinstance(transforms, Sequence):
            raise TypeError("Argument transforms should be a sequence of callables")
        if p is None:
            p = [1] * len(transforms)
        elif len(p) != len(transforms):
            raise ValueError(
                f"Length of p doesn't match the number of transforms: {len(p)} != {len(transforms)}"
            )

        if n_subset is None:
            n_subset = len(transforms)
        elif not isinstance(n_subset, int):
            raise TypeError("n_subset should be an int or None")
        elif not (1 <= n_subset <= len(transforms)):
            raise ValueError(f"n_subset should be in the interval [1, {len(transforms)}]")

        self.transforms = transforms
        total = sum(p)
        self.p = [prob / total for prob in p]
        self.n_subset = n_subset
        self.random_order = random_order

        self.selected_transforms = None

    def forward(self, *inputs: Any) -> Any:
        needs_unpacking = len(inputs) > 1

        selected_indices = torch.multinomial(torch.tensor(self.p), self.n_subset)
        if not self.random_order:
            selected_indices = selected_indices.sort().values

        self.selected_transforms = [self.transforms[i] for i in selected_indices]

        for transform in self.selected_transforms:
            outputs = transform(*inputs)
            inputs = outputs if needs_unpacking else (outputs,)

        return outputs

    def extra_repr(self) -> str:
        return (
            f"transforms={self.transforms}, "
            f"p={self.p}, "
            f"n_subset={self.n_subset}, "
            f"random_order={self.random_order}"
        )


class SharpnessJitter(Transform):
    """Randomly change the sharpness of an image or video.

    Similar to a v2.RandomAdjustSharpness with p=1 and a sharpness_factor sampled randomly.
    While v2.RandomAdjustSharpness applies — with a given probability — a fixed sharpness_factor to an image,
    SharpnessJitter applies a random sharpness_factor each time. This is to have a more diverse set of
    augmentations as a result.

    A sharpness_factor of 0 gives a blurred image, 1 gives the original image while 2 increases the sharpness
    by a factor of 2.

    If the input is a :class:`torch.Tensor`,
    it is expected to have [..., 1 or 3, H, W] shape, where ... means an arbitrary number of leading dimensions.

    Args:
        sharpness: How much to jitter sharpness. sharpness_factor is chosen uniformly from
            [max(0, 1 - sharpness), 1 + sharpness] or the given
            [min, max]. Should be non negative numbers.
    """

    def __init__(self, sharpness: float | Sequence[float]) -> None:
        super().__init__()
        self.sharpness = self._check_input(sharpness)

    def _check_input(self, sharpness):
        if isinstance(sharpness, (int, float)):
            if sharpness < 0:
                raise ValueError("If sharpness is a single number, it must be non negative.")
            sharpness = [1.0 - sharpness, 1.0 + sharpness]
            sharpness[0] = max(sharpness[0], 0.0)
        elif isinstance(sharpness, collections.abc.Sequence) and len(sharpness) == 2:
            sharpness = [float(v) for v in sharpness]
        else:
            raise TypeError(f"{sharpness=} should be a single number or a sequence with length 2.")

        if not 0.0 <= sharpness[0] <= sharpness[1]:
            raise ValueError(f"sharpness values should be between (0., inf), but got {sharpness}.")

        return float(sharpness[0]), float(sharpness[1])

    def make_params(self, flat_inputs: list[Any]) -> dict[str, Any]:
        sharpness_factor = torch.empty(1).uniform_(self.sharpness[0], self.sharpness[1]).item()
        return {"sharpness_factor": sharpness_factor}

    def transform(self, inpt: Any, params: dict[str, Any]) -> Any:
        sharpness_factor = params["sharpness_factor"]
        return self._call_kernel(F.adjust_sharpness, inpt, sharpness_factor=sharpness_factor)


class SaveTransformedImages(Transform):
    """Save transformed images at specific training steps for debugging purposes.

    This transform saves the images after all other transforms have been applied,
    allowing you to see what the model actually receives as input.

    Args:
        save_dir: Directory to save the images
        target_steps: List of training steps at which to save images
        save_original: Whether to also save the original (untransformed) image
    """

    # Class variables to track global state
    _global_step_counter = 0
    _saved_steps_per_camera = {}
    _save_executor = None

    def __init__(self, save_dir: str = "transformed_images", target_steps: list[int] = None, save_original: bool = False) -> None:
        super().__init__()
        self.save_dir = save_dir
        self.target_steps = target_steps if target_steps is not None else [0, 5, 15, 25]
        self.save_original = save_original

        os.makedirs(self.save_dir, exist_ok=True)

        # Initialize thread pool for async saving
        if SaveTransformedImages._save_executor is None:
            import concurrent.futures
            SaveTransformedImages._save_executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=2, thread_name_prefix="SaveTransformedImages"
            )

        print(f"📸 SaveTransformedImages: 将在步骤 {self.target_steps} 保存变换后的图像到 {self.save_dir}/")

    def forward(self, *inputs: Any) -> Any:
        # Apply this transform at the end of the transform chain
        # So we get the final transformed image that goes to the model

        # Check if we should save at this step (use current step, not incremented step)
        current_step = SaveTransformedImages._global_step_counter
        should_save = (current_step in self.target_steps and
                      len(inputs) > 0 and isinstance(inputs[0], torch.Tensor))

        if should_save:
            # Get camera name from call stack
            camera_name = self._get_camera_name_from_stack()

            # Check if this camera at this step has already been saved
            if camera_name not in SaveTransformedImages._saved_steps_per_camera:
                SaveTransformedImages._saved_steps_per_camera[camera_name] = set()

            if current_step not in SaveTransformedImages._saved_steps_per_camera[camera_name]:
                # Save the transformed image asynchronously
                self._async_save_image(inputs[0], camera_name, "transformed")
                SaveTransformedImages._saved_steps_per_camera[camera_name].add(current_step)

        # Return inputs unchanged (this is a monitoring transform)
        return inputs[0] if len(inputs) == 1 else inputs

    def _get_camera_name_from_stack(self) -> str:
        """Extract camera name from the call stack."""
        try:
            frame = inspect.currentframe()
            while frame:
                frame_locals = frame.f_locals
                # Look for camera-related variables in the call stack
                for var_name, var_value in frame_locals.items():
                    if 'cam' in var_name.lower() and isinstance(var_value, str):
                        return var_value
                    elif var_name == 'cam' and isinstance(var_value, str):
                        return var_value
                frame = frame.f_back
            return "unknown_camera"
        except Exception:
            return "unknown_camera"

    def _async_save_image(self, img: torch.Tensor, camera_name: str, image_type: str) -> None:
        """Asynchronously save image to avoid blocking training."""
        if SaveTransformedImages._save_executor is not None:
            SaveTransformedImages._save_executor.submit(self._save_image, img.clone(), camera_name, image_type)

    def _save_image(self, img: torch.Tensor, camera_name: str, image_type: str) -> None:
        """Save the image to disk."""
        try:
            from torchvision.transforms import ToPILImage

            to_pil = ToPILImage()

            # Handle batch data: only save the first sample
            if img.dim() == 4:  # BCHW format (batch)
                img_to_save = img[0]
            elif img.dim() == 3:  # CHW format (single image)
                img_to_save = img
            else:
                print(f"⚠️  不支持的图像维度: {img.dim()}D")
                return

            # Move to CPU to save GPU memory
            img_cpu = img_to_save.detach().cpu()

            # Ensure image values are in [0,1] range
            img_norm = torch.clamp(img_cpu, 0, 1)

            # Clean camera name for filename
            clean_camera_name = camera_name.replace(".", "_").replace("/", "_").replace("\\", "_")

            # Save image with descriptive filename
            img_pil = to_pil(img_norm)
            save_path = f"{self.save_dir}/step_{SaveTransformedImages._global_step_counter:03d}_{clean_camera_name}_{image_type}.png"
            img_pil.save(save_path)

            print(f"📸 步骤 {SaveTransformedImages._global_step_counter} ({camera_name}): {image_type}图像已保存到 {save_path}")

        except Exception as e:
            print(f"⚠️  保存{image_type}图像时出错: {e}")

    @classmethod
    def increment_global_step(cls):
        """Increment the global step counter."""
        cls._global_step_counter += 1

    @classmethod
    def cleanup(cls):
        """Clean up resources."""
        if cls._save_executor is not None:
            cls._save_executor.shutdown(wait=True)
            cls._save_executor = None


@dataclass
class ImageTransformConfig:
    """
    For each transform, the following parameters are available:
      weight: This represents the multinomial probability (with no replacement)
            used for sampling the transform. If the sum of the weights is not 1,
            they will be normalized.
      type: The name of the class used. This is either a class available under torchvision.transforms.v2 or a
            custom transform defined here.
      kwargs: Lower & upper bound respectively used for sampling the transform's parameter
            (following uniform distribution) when it's applied.
    """

    weight: float = 1.0
    type: str = "Identity"
    kwargs: dict[str, Any] = field(default_factory=dict)


@dataclass
class ImageTransformsConfig:
    """
    These transforms are all using standard torchvision.transforms.v2
    You can find out how these transformations affect images here:
    https://pytorch.org/vision/0.18/auto_examples/transforms/plot_transforms_illustrations.html
    We use a custom RandomSubsetApply container to sample them.
    """

    # Set this flag to `true` to enable transforms during training
    enable: bool = False
    # This is the maximum number of transforms (sampled from these below) that will be applied to each frame.
    # It's an integer in the interval [1, number_of_available_transforms].
    max_num_transforms: int = 3
    # By default, transforms are applied in Torchvision's suggested order (shown below).
    # Set this to True to apply them in a random order.
    random_order: bool = False

    # Enable saving transformed images (save images after transforms are applied)
    enable_save_transformed: bool = False
    # Directory to save transformed images
    save_transformed_dir: str = "transformed_images"
    # Steps at which to save transformed images
    save_transformed_steps: list[int] = field(default_factory=lambda: [0, 5, 15, 25])
    # Whether to also save original images for comparison
    save_original_images: bool = False
    tfs: dict[str, ImageTransformConfig] = field(
        default_factory=lambda: {
            "brightness": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"brightness": (0.8, 1.2)},
            ),
            "contrast": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"contrast": (0.8, 1.2)},
            ),
            "saturation": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"saturation": (0.5, 1.5)},
            ),
            "hue": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"hue": (-0.05, 0.05)},
            ),
            "sharpness": ImageTransformConfig(
                weight=1.0,
                type="SharpnessJitter",
                kwargs={"sharpness": (0.5, 1.5)},
            ),
            "save_transformed_images": ImageTransformConfig(
                weight=0.0,  # 默认禁用，通过 enable_save_transformed 控制
                type="SaveTransformedImages",
                kwargs={"save_dir": "transformed_images", "target_steps": [0, 5, 15, 25], "save_original": False},
            ),
        }
    )


def make_transform_from_config(cfg: ImageTransformConfig):
    if cfg.type == "Identity":
        return v2.Identity(**cfg.kwargs)
    elif cfg.type == "ColorJitter":
        return v2.ColorJitter(**cfg.kwargs)
    elif cfg.type == "SharpnessJitter":
        return SharpnessJitter(**cfg.kwargs)
    elif cfg.type == "SaveTransformedImages":
        return SaveTransformedImages(**cfg.kwargs)
    else:
        raise ValueError(f"Transform '{cfg.type}' is not valid.")


class ImageTransforms(Transform):
    """A class to compose image transforms based on configuration."""

    def __init__(self, cfg: ImageTransformsConfig) -> None:
        super().__init__()
        self._cfg = cfg

        self.weights = []
        self.transforms = {}
        self.always_apply_transforms = []  # 始终应用的变换

        # 根据配置参数动态调整变换权重和参数
        modified_tfs = dict(cfg.tfs)

        # 处理保存变换后图像的变换
        if "save_transformed_images" in modified_tfs:
            if cfg.enable_save_transformed:
                # 启用保存变换后图像
                modified_tfs["save_transformed_images"] = ImageTransformConfig(
                    weight=1.0,
                    type="SaveTransformedImages",
                    kwargs={
                        "save_dir": cfg.save_transformed_dir,
                        "target_steps": cfg.save_transformed_steps,
                        "save_original": cfg.save_original_images
                    }
                )
                # 这个变换应该始终应用，且在最后应用
                self.always_apply_transforms.append(make_transform_from_config(modified_tfs["save_transformed_images"]))
            else:
                # 禁用保存变换后图像
                modified_tfs["save_transformed_images"] = ImageTransformConfig(
                    weight=0.0,
                    type="SaveTransformedImages",
                    kwargs={
                        "save_dir": cfg.save_transformed_dir,
                        "target_steps": cfg.save_transformed_steps,
                        "save_original": cfg.save_original_images
                    }
                )

        # 处理其他变换（排除保存变换后图像的变换）
        for tf_name, tf_cfg in modified_tfs.items():
            if tf_name == "save_transformed_images":
                continue  # 已经在上面处理过了
            if tf_cfg.weight <= 0.0:
                continue

            self.transforms[tf_name] = make_transform_from_config(tf_cfg)
            self.weights.append(tf_cfg.weight)

        n_subset = min(len(self.transforms), cfg.max_num_transforms)
        if n_subset == 0 or not cfg.enable:
            self.main_tf = v2.Identity()
        else:
            self.main_tf = RandomSubsetApply(
                transforms=list(self.transforms.values()),
                p=self.weights,
                n_subset=n_subset,
                random_order=cfg.random_order,
            )

    def forward(self, *inputs: Any) -> Any:
        # 首先应用主要的变换
        outputs = self.main_tf(*inputs)

        # 然后应用始终应用的变换（如保存图像）
        for transform in self.always_apply_transforms:
            if isinstance(outputs, tuple):
                outputs = transform(*outputs)
            else:
                outputs = transform(outputs)

        return outputs
