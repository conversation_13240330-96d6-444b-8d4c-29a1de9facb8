# 只使用3个主要相机（排除一个相机）
python -m lerobot.scripts.train \
--policy.path=lerobot/pi0 \
--dataset.repo_id=stock_cloth_2 \
--dataset.root=/home/<USER>/data/stock_cloth_2 \
--dataset.episodes="[$(python -c 'print(",".join(map(str, range(0, 1000))))'))]" \
--dataset.image_transforms.enable=true \
--output_dir=outputs/train/pi0_stock_cloth_3cam \
--job_name=pi0_stock_cloth_3cam \
--steps=50000 \
--batch_size=16 \
--eval_freq=0